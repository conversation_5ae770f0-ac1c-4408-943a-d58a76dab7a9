import { v4 as uuidv4 } from 'uuid';
import dayjs from 'dayjs';

export const initialState = {
  toggleRejectedBatches: false,
  isBulkUpload: false,
  isBatchValid: false,
  data: [
    {
      key: uuidv4(), asset: '', product_sku_name: '', availableQuantity: '', thresholdQuantity: '', quantity: null,
    },
  ],
  adjustmentTypes: [
    'INWARDS',
    'OUTWARD',
    'RECONCILIATION',
  ],
  adjustmentBasedOn: 'CURRENT_STOCK',
  allStock: true,
  selectedDate: dayjs(),
  selectedAdjustmentType: 'INWARDS',
};

export const reducer = (state, action) => {
  switch (action.type) {
  case 'SET_FIELD': {
    return {
      ...state,
      [action.field]: action.value,
    };
  }

  case 'SET_MULTIPLE_FIELDS': {
    return {
      ...state,
      ...action.payload,
    };
  }

  case 'HANDLE_PRODUCT_CHANGE_VALUE': {
    const { value, key } = action.payload;

    const updatedData = state?.data?.map((item) =>
      item?.key === key
        ? { ...item, product_sku_name: value }
        : item
    );

    return {
      ...state,
      data: updatedData,
    };
  }

  case 'HANDLE_PRODUCT_CHANGE': {
    const { tenantSku, key, otherDepStocksData, isMultiMode, callback, user, inventoryLocations, cfV2DocStockAdjustment,
    } = action.payload;

    const { data, userTenantDepartmentId, selectedAdjustmentType } = state;

    const autoPrintDescription = user?.tenant_info?.global_config?.settings?.print_description_automatically;
    const batchCustomFields = CustomFieldHelpers.getCfStructure(cfV2DocStockAdjustment?.data?.batch_custom_fields, true);

    if (isMultiMode && Array.isArray(tenantSku)) {
      const copyData = [];

      tenantSku.forEach((tenantSkuData) => {
        const currentOtherDepStocksData = otherDepStocksData?.find((item) => item?.product_sku_id === tenantSkuData?.product_sku_id);

        const newData = {
          key: uuidv4(),
        };

        const copyDataItem = createData({
          dataItem: newData,
          tenantSku: tenantSkuData,
          otherDepStocksData: currentOtherDepStocksData,
          userTenantDepartmentId,
          inventoryLocations,
          selectedAdjustmentType,
          autoPrintDescription,
          batchCustomFields,
        });

        copyData.push(copyDataItem);
      });

      if (callback) {
        callback();
      }

      return {
        ...state,
        data: [...state.data, ...copyData],
      };
    } else {
      data?.map((item) => {
        if (item.key === key) {
          const copyDataItem = createData({
            dataItem: item,
            tenantSku,
            otherDepStocksData,
            userTenantDepartmentId,
            inventoryLocations,
            selectedAdjustmentType,
            autoPrintDescription,
            batchCustomFields,
          });

          return copyDataItem;
        }
        return item;
      });

      return {
        ...state,
        data,
      };
    }
  }

  default: {
    return state;
  }
  };
};
