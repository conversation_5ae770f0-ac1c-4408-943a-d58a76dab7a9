import { INFINITE_EXPIRY_DATE } from "@Apis/constants";
import dayjs from "dayjs";

export const createData = ({
  dataItem, tenantSku, otherDepStocksData, userTenantDepartmentId, inventoryLocations, selectedAdjustmentType, autoPrintDescription, batchCustomFields,
}) => {
  const copyDataItem = dataItem;

  const newBatch = {
    tenant_department_id: userTenantDepartmentId,
    tenant_product_id: tenantSku?.tenant_product_id,
    expiry_date: tenantSku?.product_info?.expiry_days > 0 ? (dayjs().endOf('day').add(tenantSku?.product_info?.expiry_days, 'day')) : dayjs(INFINITE_EXPIRY_DATE, 'YYYY-MM-DD').endOf('day'),
    lot_number: '',
    custom_batch_number: `${tenantSku?.product_info?.internal_sku_code}/${dayjs().format('DDMMYY')}/${tenantSku?.product_info?.product_batch_counter}`,
    cost_price: tenantSku?.cost_price || 0,
    mrp: tenantSku?.mrp || 0,
    selling_price: tenantSku?.selling_price || 0,
    uom_id: tenantSku?.uom_id,
    manual_entry: false,
    inventory_location_id: inventoryLocations?.inventory_location?.filter((i) => i?.is_active)?.[0]?.inventory_location_id,
    inventory_location_path: inventoryLocations?.inventory_location?.filter((i) => i?.is_active)?.[0]?.inventory_location_path,
    is_rejected_batch: false,
    custom_fields: batchCustomFields,
  };
  const batches = tenantSku?.product_batches;
  if (tenantSku?.barcode_batch_id) {
    for (let j = 0; j < batches?.length; j++) {
      if (tenantSku?.barcode_batch_id === batches[j]?.batch_id) {
        batches[j].consumed_qty = 1;
        batches[j].batch_in_use = true;
      }
    }
  } else {
    for (let j = 0; j < batches?.length; j++) {
      batches[j].consumed_qty = 0;
      batches[j].batch_in_use = true;
    }
  }
  copyDataItem.product_sku_name = `${tenantSku?.product_info.product_sku_name}`;
  copyDataItem.otherDepartmentStock = otherDepStocksData?.tenant_products;
  copyDataItem.product_sku_id = tenantSku?.product_info.product_sku_id;
  copyDataItem.availableQuantity = tenantSku?.available_qty;
  copyDataItem.thresholdQuantity = `${tenantSku?.threshold_qty}`;
  copyDataItem.tenantProductId = `${tenantSku?.tenant_product_id}`;
  copyDataItem.asset = tenantSku?.product_info?.assets || [];
  copyDataItem.selectedBatch = newBatch;
  copyDataItem.expiryDateFormat = tenantSku?.expiry_date_format;
  copyDataItem.manufacturingDateFormat = tenantSku?.manufacturing_date_format;
  copyDataItem.available_batches = selectedAdjustmentType !== 'OUTWARD'
    ? (tenantSku?.product_batches
      ? [
        {
          ...newBatch,
        },
        ...tenantSku?.product_batches.map((batch) => ({
          ...batch,
          custom_fields: CustomFieldHelpers.getCfStructure(batch?.custom_fields, true),
        })),
      ]
      : [
        {
          ...newBatch,
        },
      ])
    : Helpers.batchSelectionMethod(
      Helpers.getBatchMethod(tenantSku),
      tenantSku?.product_batches.map((batch) => ({
        ...batch,
        custom_fields: CustomFieldHelpers.getCfStructure(batch?.custom_fields, true),
      })),
    );
  copyDataItem.expiry_days = tenantSku?.product_info?.expiry_days;
  copyDataItem.expiryDate = tenantSku?.product_info?.expiry_days >= 0 ? (toISTDate(dayjs()).add(tenantSku?.product_info?.expiry_days || 0, 'day')).format('YYYY-MM-DD') : INFINITE_EXPIRY_DATE;
  copyDataItem.unit = tenantSku?.product_info?.unit;
  copyDataItem.uom_info = tenantSku?.uom_info;
  copyDataItem.uom_list = tenantSku?.uom_list;
  copyDataItem.sku = tenantSku.product_info.product_sku_id ? tenantSku.product_info.product_sku_id : '';
  copyDataItem.expiryDays = tenantSku?.product_info?.expiry_days;
  copyDataItem.internal_sku_code = tenantSku?.product_info?.internal_sku_code;
  copyDataItem.ref_product_code = tenantSku?.product_info?.ref_product_code;
  copyDataItem.quantity = tenantSku?.barcode_batch_id ? 1 : 0;
  copyDataItem.nextBatchCode = `${tenantSku?.product_info?.internal_sku_code}/${dayjs().format('DDMMYY')}/${tenantSku?.product_info?.product_batch_counter}`;
  copyDataItem.batchConsumptionMethod = Helpers.getBatchMethod(tenantSku);
  copyDataItem.remarks = autoPrintDescription ? (otherDepStocksData?.description || '')?.replace(/<[^>]+>/g, '') : '';
  copyDataItem.product_category_info = tenantSku?.product_category_info;
  copyDataItem.secondaryUomUqc = tenantSku?.secondary_uom_info?.uqc;
  copyDataItem.secondaryAvailableQty = tenantSku?.secondary_available_qty;
  copyDataItem.secondary_uom_qty = 0;
  copyDataItem.secondaryUomId = tenantSku?.secondary_uom_id;

  return copyDataItem;
};