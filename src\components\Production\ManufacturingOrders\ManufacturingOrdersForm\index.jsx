// 📦 React & Hooks
import React, { Component, Fragment } from 'react';

// 🚦 Routing
import { withRouter } from 'react-router-dom';

// 🧩 Redux
import { connect } from 'react-redux';

// 🧾 Redux Actions
import MOActions from '@Actions/moActions';
import OfferActions from '@Actions/offerActions';
import BOMActions from '@Actions/bomActions';
import JobWorkActions from '@Actions/production/jobWorkActions';
import CFV2Actions from '@Actions/configurations/cfV2Actions';
import ProductActions from '@Actions/productActions';
import CMActions from '@Actions/settings/cmActions';

// 📅 Utilities
import dayjs from 'dayjs';
import { v4 as uuidv4 } from 'uuid';
import constants, { DEFAULT_CUR_ROUND_OFF, QUANTITY, toISTDate } from '@Apis/constants';
import Decimal from 'decimal.js';

// 🧠 Helpers & Constants
import Helpers from '@Apis/helpers';
import FormHelpers from '@Helpers/FormHelpers';
import CustomFieldHelpers from '@Helpers/CustomFieldHelpers';
import {
  addBomLineIdAtChildBomLinesOfItsParent,
  addNewRmRowAtChildBomLinesRecursively,
  addProductInfoOfRmRowAtChildBomLinesRecursively,
  createBomDataFromProductData,
  createBomLineDataFromProductData,
  createChildBomLineDataFromProductData,
  createDefaultBomLineData,
  createDefaultChildBomLine,
  getDataByEmptyRowAtLast,
} from './helpers';
import moErrorsList from './MoErrors';

// 🔧 API Hooks
// (No API hooks present)

// 🎨 UI Components - H3 UI Kit
import H3Text from '@Uilib/h3Text';
import H3FormInput from '@Uilib/h3FormInput';
import PRZButton from '../../../Common/UI/PRZButton';
import PRZInput from '../../../Common/UI/PRZInput';
import PRZText from '../../../Common/UI/PRZText';
import PRZSelect from '../../../Common/UI/PRZSelect';

// 🎛️ Common Components
import SelectSeller from '@Components/Common/SelectSeller';
import AddressSelector from '@Components/Common/FormUtils/AddressSelecter';
import TenantSelector from '@Components/Common/Selector/TenantSelector';
import SelectAppUser from '@Components/Common/SelectAppUser';
import CustomFieldV3 from '@Components/Common/CustomFieldV3';
import CustomDocumentInputs from '@Components/Common/CustomDocumentInputs';
import CustomDocumentColumns from '@Components/Common/CustomDocumentColumns';
import SelectDepartment from '../../../Common/SelectDepartment';
import RestrictedAccessMessage from '../../../Common/RestrictedAccess/RestrictedAccessMessage';
import ErrorHandle from '../../../Common/ErrorHandle';
import DocumentNumberSeqInput from '../../../Admin/Common/DocumentNumberSeqInput';

// 📄 Module-Specific Components
import FinishedGood from '@Components/Production/ManufacturingOrders/ManufacturingOrdersForm/Tables/FinishedGood';
import ProductionRoute from './Tables/ProductionRoute';
import OtherCharge from './Tables/OtherCharge';
import ByProduct from './Tables/ByProduct';
import RawMaterial from './Tables/RawMaterial';
import NewRawMaterial from './Tables/NewRawMaterial';
import PRZConfirmationPopover from '../../../Common/UI/PRZConfirmationPopover';

// 🧰 Ant Design Components
import { Badge, Checkbox, DatePicker, Drawer, Popconfirm, Select, Tabs, Upload, notification } from 'antd';

// 🎨 Icons
import { EditFilled, PlusCircleFilled, PlusOutlined } from '@ant-design/icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlusCircle } from '@fortawesome/free-solid-svg-icons';

// 🖼️ Static Assets
import { cdnUrl } from '@Utils/cdnHelper';

// 🖌️ Styles
import './style.scss';

const { TabPane } = Tabs;

const uploadButton = (
  <div>
    <PlusOutlined />
    <div style={{ marginTop: 8 }}>Upload</div>
  </div>
);

class ManufacturingOrderForm extends Component {
  constructor(props) {
    super(props);
    this.state = {
      finishedGoods: [],
      trackWastage: true,
      batchLevelJobWorks: true,
      bomByProducts: {},
      extraCharges: {},
      bomLines: {},
      productionRoutes: {},
      tenantDepartmentId: '',
      paymentTerms: 0,
      scheduledDate: dayjs(),
      deliveryDate: '',
      attachments: [],
      selectedTenant: '',
      currentTab: '/finished-goods',
      showAdvancedSettings: false,
      restrictMoUser: false,
      moUsers: [],
      restrictionCheck: true,
      isIssueSfgEnabled: false,
      visibleColumnsFG: {
        PRODUCT: {
          label: 'Product',
          visible: true,
          disabled: true,
        },
        READY_TO_PRODUCE: {
          label: 'Ready To Produce',
          visible: true,
          disabled: true,
        },
        REQUIRED_QUANTITY: {
          label: 'Required Quantity',
          visible: true,
          disabled: true,
        },
        RM_COST: {
          label: 'Rm Cost Per Unit',
          visible: true,
          disabled: true,
        },
        CHARGES: {
          label: 'Charges Per Unit',
          visible: true,
          disabled: true,
        },
        TOTAL_UNIT_COST: {
          label: 'Total Unit Cost',
          visible: true,
          disabled: true,
        },
        TOTAL_COST: {
          label: 'Total Cost',
          visible: true,
          disabled: true,
        },
      },
      visibleColumnsRM: {
        PRODUCT: {
          label: 'Product',
          visible: true,
          disabled: true,
        },
        CURRENT_STOCK: {
          label: 'Current Stock',
          visible: true,
          disabled: true,
        },
        ESTIMATED_USAGE: {
          label: 'Estimated Usage',
          visible: true,
          disabled: true,
        },
        ESTIMATED_WASTAGE: {
          label: 'Estimated Overage',
          visible: true,
          disabled: true,
        },
        UNIT_COST: {
          label: 'Unit Cost',
          visible: true,
          disabled: true,
        },
        ESTIMATED_TOTAL_COST: {
          label: 'Estimted Total Cost',
          visible: true,
          disabled: true,
        },
        UNIT: {
          label: 'Unit',
          visible: true,
          disabled: false,
        },
      },
      updateDocumentReason: '',
      costingMethod: 'NORMAL',
    };
  }

  componentDidMount() {
    const {
      getMOById, user, match, getDocCFV2, getMoJobWorks, getJobWorkByMoIdSuccess, location, getCM,
    } = this.props;
    const { moId } = match.params;
    const moIdClone = location?.state?.cloneMo;
    const MoId = moIdClone || moId;
    if (MoId) {
      getJobWorkByMoIdSuccess(null);
      getMOById(Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.MO, Helpers.permissionTypes.UPDATE).join(',') || user?.tenant_info?.tenant_id, MoId);
      getMoJobWorks({ mo_id: MoId }, () => { });
    } else {
      // create case
      this.setState({
        selectedTenant: user?.tenant_info?.tenant_id,
        tenantDepartmentId: user?.tenant_info?.default_department_for_production,
        rmTenantDepartmentId: user?.tenant_info?.default_department_for_rm,
        fgTenantDepartmentId: user?.tenant_info?.default_department_for_fg,
        bpTenantDepartmentId: user?.tenant_info?.default_department_for_bp,
      });
    }
    const payload = {
      orgId: user?.tenant_info?.org_id,
      entityName: 'MANUFACTURING_ORDER',
    };
    getDocCFV2(payload);
    getCM(user?.tenant_info?.org_id, '', 'BILL_OF_MATERIAL', null, null, null, 'CUSTOM_CHARGE');
  }

  componentWillUnmount() {
    const {
      getMOByIdSuccess, getJobWorkByMoIdSuccess, getBOMByIdSuccess,
    } = this.props;
    getBOMByIdSuccess(null);
    getMOByIdSuccess(null);
    getJobWorkByMoIdSuccess(null);
  }

  getManufacturingOrderErrors() {
    const {
      cfManufacturingOrderDoc, deliveryDate, scheduledDate, finishedGoods, bomLines, bomByProducts, restrictMoUser, moUsers, extraCharges, trackWastage,
    } = this.state;
    const { docLevelError, lineLevelError } = moErrorsList({
      cfManufacturingOrderDoc, deliveryDate, scheduledDate, finishedGoods, bomLines, bomByProducts, restrictMoUser, moUsers, extraCharges, trackWastage,
    });
    return { docLevelError: docLevelError ?? [], lineLevelError: lineLevelError ?? [] };
  }

  static getDerivedStateFromProps(props, state) {
    if (props.cfV2DocManufacturingOrder && !state.isUserReadyForCF) {
      const rmLineCFs = CustomFieldHelpers.getCfStructure(props.cfV2DocManufacturingOrder?.data?.document_line_custom_fields?.filter((item) => item.entity_name === 'MANUFACTURING_ORDER_LINE' && item?.is_active), false);
      const fgLineCFs = CustomFieldHelpers.getCfStructure(
        props.cfV2DocManufacturingOrder?.data?.document_line_custom_fields
          ?.filter((item) => item.entity_name === 'MANUFACTURING_ORDER_FINISHED_GOOD' && item?.is_active)
          ?.filter((item) => item?.is_active), false,
      );
      return {
        ...state,
        cfManufacturingOrderDoc: CustomFieldHelpers.getCfStructure(props.cfV2DocManufacturingOrder?.data?.document_custom_fields, true) || [],
        cfRMLine: rmLineCFs,
        initialCfRMLine: rmLineCFs,
        cfFGLine: fgLineCFs,
        initialCfFGLine: fgLineCFs,
        visibleColumnsFG: CustomFieldHelpers.updateVisibleColumns(props.cfV2DocManufacturingOrder?.data?.document_line_custom_fields?.filter((item) => item.entity_name === 'MANUFACTURING_ORDER_FINISHED_GOOD'), state.visibleColumnsFG),
        // finishedGoods: state.finishedGoods.map((item) => ({ ...item, lineCustomFields: fgLineCFs })),
        isUserReadyForCF: true,
      };
    }

    const getBomLinesWithKeys = (lines, fgQuantity, key) => {
      const { costingMethod } = state;
      let rawMaterialLines = [];
      const getMoRawMaterials = (bomLines, parentQuantity, parentKey, isChildBom) => {
        for (let i = 0; i < bomLines?.length; i++) {
          const item = bomLines[i];
          const currentParentkey = uuidv4();
          const costPrice = costingMethod === '3_MONTH_WEIGHTED_AVERAGE'
            ? (item?.tenant_product_info?.weighted_avg_cost_of_last_3_months || 0) : costingMethod === '6_MONTH_WEIGHTED_AVERAGE'
              ? (item?.tenant_product_info?.weighted_avg_cost_of_last_6_months || 0) : (item?.tenant_product_info?.cost_price || 0);
          const bomLine = {
            ...item,
            key: currentParentkey,
            parentKey,
            bomLineId: item?.bom_line_id,
            productSkuName: item?.tenant_product_info?.product_sku_name,
            product_category_info: item?.tenant_product_info?.product_category_info,
            tenant_product_id: item?.tenant_product_info?.tenant_product_id,
            tenantProductId: item?.tenant_product_info?.tenant_product_id,
            uomInfo: item?.uom_info,
            uom_info: item?.uom_info,
            uomId: item?.uom_info?.uom_id,
            currentStock: item?.tenant_product_info?.total_available_batches_quantity,
            bom_cost: item?.mo_line_cost_price,
            product_info: item?.tenant_product_info,
            costPerUnit: ((costPrice / item?.tenant_product_info?.uom_info?.ratio) * item?.uom_info?.ratio),
            quantityPerUnit: item?.parent_quantity_per_unit,
            parentQuantityPerUnit: item?.parent_quantity_per_unit,
            fgQuantity,
            estimatedUsage: item?.quantity,
            wastage_percentage: item?.bom_line_info?.wastage_percentage,
            child_bom_lines: item?.child_mo_lines,
            child_bom_id: item?.bom_line_child_bom_id ?? null,
            parent_bom_id: item?.bom_line_parent_bom_id ?? null,
            lineCustomFields: CustomFieldHelpers.getDocumentCf(props.cfV2DocManufacturingOrder?.data?.document_line_custom_fields?.filter((item) => item.entity_name === 'MANUFACTURING_ORDER_LINE'), item?.mo_line_custom_fields)?.map((i) => ({
              ...i,
              fieldValue: (i?.fieldName === 'Quantity' ? Number(item?.quantity) : (i?.fieldType === 'NUMBERS' ? Number(i?.fieldValue) : i?.fieldValue)),
            })),
            isChildBom: isChildBom ?? false,
            // lineCustomFields: CustomFieldHelpers.getDocumentCf(item?.mo_line_custom_fields, item?.mo_line_custom_fields)?.map((i) => ({
            //   ...i,
            //   fieldValue: (i?.fieldName === 'Quantity' ? Number(item?.quantity) : i?.fieldValue),
            // })),
          };

          if (!item.bom_line_id) {
            const bomLineId = uuidv4();
            bomLine.bomLineId = bomLineId;
            bomLine.bom_line_id = bomLineId;
            bomLine.isAdhocLine = true;

            rawMaterialLines = addBomLineIdAtChildBomLinesOfItsParent({ bomLines: rawMaterialLines, bomLineId, moLineId: bomLine?.mo_line_id });
          }

          rawMaterialLines.push(bomLine);
          getMoRawMaterials(item?.child_mo_lines, item?.quantity, currentParentkey, true);
        }
        return rawMaterialLines;
      };
      return getMoRawMaterials(lines, fgQuantity, key);
    };
    if (props?.selectedMO && props?.moJobWorks && props.cfV2DocManufacturingOrder?.data?.success && (props?.match?.params?.moId || props?.location?.state?.cloneMo) && !state.isUserReadyForUpdate) {
      const rmMoLineCustomField = props.cfV2DocManufacturingOrder?.data?.document_line_custom_fields?.filter((item) => item.entity_name === 'MANUFACTURING_ORDER_LINE' && item?.is_active);
      const fgMoLineCustomField = props.cfV2DocManufacturingOrder?.data?.document_line_custom_fields?.filter((item) => item.entity_name === 'MANUFACTURING_ORDER_FINISHED_GOOD' && item?.is_active);
      const rmLineCFs = CustomFieldHelpers.getCfStructure(props.cfV2DocManufacturingOrder?.data?.document_line_custom_fields?.filter((item) => item.entity_name === 'MANUFACTURING_ORDER_LINE' && item?.is_active), false);
      const fgLineCFs = CustomFieldHelpers.getCfStructure(props.cfV2DocManufacturingOrder?.data?.document_line_custom_fields?.filter((item) => item.entity_name === 'MANUFACTURING_ORDER_FINISHED_GOOD' && item?.is_active), false);
      const tempSelectedMO = props.selectedMO;
      const formattedBomLines = {};
      const fgTpIds = tempSelectedMO?.mo_finished_goods?.map((item) => item?.tenant_product_id);
      for (let i = 0; i < fgTpIds?.length; i++) {
        let bomLines = [];

        if (tempSelectedMO?.linked_sales_orders?.length) {
          bomLines = tempSelectedMO?.mo_lines_first_level_exploded_view
            ?.filter((item) => item?.fg_tenant_product_id === fgTpIds[i])
            ?.map((item) => {
              const bomLine = {
                ...item,
                mo_line_custom_fields:
                  item?.mo_line_custom_fields?.length > 1
                    ? item?.mo_line_custom_fields
                    : rmMoLineCustomField,
              };

              if (!item.bom_line_id) {
                bomLine.bom_line_id = uuidv4();
                bomLine.isAdhocLine = true;
              }

              return bomLine;
            });
        } else {
          bomLines = tempSelectedMO?.mo_lines_first_level_exploded_view?.filter(
            (item) => item?.fg_tenant_product_id === fgTpIds[i],
          )?.map((item) => {
            if (!item.bom_line_id) {
              if (!item.mo_line_custom_fields) {
                item.mo_line_custom_fields = rmMoLineCustomField;
              }
              return {
                ...item,
                bom_line_id: uuidv4(),
                isAdhocLine: true,
              };
            }
            return item;
          });
        }
        const bomLinesWithKeys = getBomLinesWithKeys(bomLines, tempSelectedMO?.mo_finished_goods?.find((item) => item?.tenant_product_id === fgTpIds[i])?.quantity, null);
        formattedBomLines[fgTpIds[i]] = bomLinesWithKeys;
      }

      const formattedByProducts = {};
      const byProducts = tempSelectedMO?.by_product_lines;
      for (let i = 0; i < byProducts?.length; i++) {
        const tenantProductId = byProducts[i]?.fg_tenant_product_id;
        const byProduct = {
          ...byProducts[i],
          bomLineId: byProducts[i]?.bom_line_id,
          productSkuName: byProducts[i]?.tenant_product_info?.product_sku_name,
          tenantProductId: byProducts[i]?.tenant_product_info?.tenant_product_id,
          product_category_info: byProducts[i]?.tenant_product_info?.product_category_info,
          uomInfo: byProducts[i]?.uom_info,
          product_info: byProducts[i]?.tenant_product_info,
          quantityPerUnit: (byProducts[i].bom_quantity_per_unit * byProducts[i]?.bom_by_product_line_info?.uom_info?.ratio) / byProducts[i]?.tenant_product_info?.uom_info?.ratio,
          fgQuantity: tempSelectedMO?.mo_finished_goods?.find((item) => item?.bom_id === byProducts[i]?.bom_id)?.quantity,
          estimatedUsage: byProducts[i]?.quantity,
          key: uuidv4(),
          parentKey: null,
        };
        if (formattedByProducts.hasOwnProperty(tenantProductId)) {
          formattedByProducts[tenantProductId].push(byProduct);
        } else formattedByProducts[tenantProductId] = [byProduct];
      }

      const formattedCharges = {};
      const extraCharges = tempSelectedMO?.extra_charges;
      for (let i = 0; i < extraCharges?.length; i++) {
        const tenantProductId = extraCharges[i]?.fg_tenant_product_id;
        const fgQty = tempSelectedMO?.mo_finished_goods?.find((item) => item?.bom_id === extraCharges[i]?.bom_id)?.quantity;
        const charge = {
          ...extraCharges[i],
          bomLineId: extraCharges[i]?.bom_line_id,
          fgQuantity: tempSelectedMO?.mo_finished_goods?.find((item) => item?.bom_id === extraCharges[i]?.bom_id)?.quantity,
          costPerUnit: (extraCharges[i].est_charge_amount / fgQty)?.toFixed(DEFAULT_CUR_ROUND_OFF),
          charge_amount: extraCharges[i].est_charge_amount,
          key: uuidv4(),
        };
        if (formattedCharges.hasOwnProperty(tenantProductId)) {
          formattedCharges[tenantProductId].push(charge);
        } else formattedCharges[tenantProductId] = [charge];
      }

      const modifiedProductionRoutes = JSON.parse(JSON.stringify(props?.moJobWorks?.job_works)); // Create a deep copy to avoid modifying the original data

      modifiedProductionRoutes?.forEach((route) => {
        route?.pr_lines?.forEach((pr_line) => {
          // Check if the assignees array is empty
          if (Array.isArray(pr_line.assignees) && pr_line.assignees.length === 0) {
            // Remove the assignees key
            delete pr_line.assignees;
          }
        });
      });
      // Production Routes
      const formattedProductionRoutes = {};

      const productionRoutes = modifiedProductionRoutes;
      for (let i = 0; i < productionRoutes?.length; i++) {
        const tenantProductId = productionRoutes[i]?.fg_tenant_product_id;

        // Skip iteration if tenantProductId is null or undefined
        if (!tenantProductId) continue;

        formattedProductionRoutes[tenantProductId] = {
          ...productionRoutes[i],
          br_lines: productionRoutes[i]?.pr_lines?.map((rec) => ({
            ...rec,
            is_subcontractor: !!rec?.subcontractor_seller_id,
            machine_resource_group: rec?.machine_resource_group?.resource_group_id,
            fixed_charge: rec?.route_line_charges?.find((item) => item?.charge_type === 'fixed')?.charge_amount,
            unit_charge: rec?.route_line_charges?.find((item) => item?.charge_type === 'unit')?.charge_per_unit,
            lead_time_per_unit:
              (rec?.lead_time_in_min || 0)
              / (tempSelectedMO?.mo_finished_goods?.find(
                (item) => item?.tenant_product_id == tenantProductId,
              )?.quantity || 1),
            key: uuidv4(),
          })),
          fg_product: tempSelectedMO?.mo_finished_goods?.find(
            (item) => item?.tenant_product_id === tenantProductId,
          ),
        };
      }
      // Already Used Custom  Fields
      const oldCustomField = tempSelectedMO?.custom_fields || [];

      const moFinishedGoods = tempSelectedMO?.linked_sales_orders?.length ? tempSelectedMO?.mo_finished_goods?.map((item) => ({
        ...item,
        key: uuidv4(),
        custom_fields: item?.custom_fields?.length > 1 ? item?.custom_fields : fgMoLineCustomField,
      })) : tempSelectedMO?.mo_finished_goods?.map((item) => ({
        ...item,
        key: uuidv4(),
        custom_fields: item?.custom_fields,
      }));
      return {
        ...state,
        finishedGoods: moFinishedGoods?.map((item) => {
          const moLine = {
            ...item,
            key: uuidv4(),
            lineCustomFields: item?.custom_fields?.length ? CustomFieldHelpers.getDocumentCf(props.cfV2DocManufacturingOrder?.data?.document_line_custom_fields?.filter((item) => item.entity_name === 'MANUFACTURING_ORDER_FINISHED_GOOD'), item?.custom_fields || []) : [],
          };
          if (item?.is_without_bom) {
            moLine.isAdhocLine = true;
          }

          return moLine;
        }),
        numberOfJobWorks: props?.selectedMO?.no_of_job_works,
        isFlexible: !!(props?.selectedMO?.bom_info?.flexible_consumption === 'ALLOWED'),
        isUserReadyForUpdate: true,
        moNumber: props?.selectedMO?.mo_number,
        defaultQty: tempSelectedMO.quantity,
        fileList: tempSelectedMO?.attachments,
        remark: tempSelectedMO?.remarks,
        scheduledDate: props?.location?.state?.cloneMo ? dayjs() : toISTDate(tempSelectedMO?.scheduled_date),
        deliveryDate: props?.location?.state?.cloneMo ? dayjs() : (tempSelectedMO?.delivery_date ? toISTDate(tempSelectedMO?.delivery_date) : null),
        selectedTenant: tempSelectedMO?.tenant_id,
        tenantDepartmentId: tempSelectedMO?.tenant_department_id,
        fgTenantDepartmentId: tempSelectedMO?.fg_tenant_department_id,
        rmTenantDepartmentId: tempSelectedMO?.rm_tenant_department_id,
        bpTenantDepartmentId: tempSelectedMO?.bp_tenant_department_id,
        bomLines: formattedBomLines,
        bomByProducts: formattedByProducts,
        extraCharges: formattedCharges,
        productionRoutes: formattedProductionRoutes,
        bomInfo: tempSelectedMO.bom_info,
        selectedMo: tempSelectedMO,
        tenantSellerId: tempSelectedMO?.tenant_seller_info?.tenant_seller_id,
        sellerId: tempSelectedMO?.tenant_seller_info?.seller_id,
        vendorAddress: tempSelectedMO?.seller_address,
        sellerName: tempSelectedMO?.seller_name,
        sellerGst: tempSelectedMO?.seller_gst,
        trackWastage: tempSelectedMO?.enable_wastage_tracking,
        batchLevelJobWorks: tempSelectedMO?.job_works_type === 'batch_level',
        cfManufacturingOrderDoc: CustomFieldHelpers.getDocumentCf(props.cfV2DocManufacturingOrder?.data?.document_custom_fields, oldCustomField),
        restrictMoUser: tempSelectedMO?.is_restricted,
        isIssueSfgEnabled: tempSelectedMO?.is_issue_sfg_enabled,
        moUsers: tempSelectedMO?.is_restricted ? tempSelectedMO?.mo_users : [],
        cfRMLine: rmLineCFs,
        initialCfRMLine: rmLineCFs,
        visibleColumnsRM: CustomFieldHelpers.updateVisibleColumns(props.cfV2DocManufacturingOrder?.data?.document_line_custom_fields?.filter((item) => item.entity_name === 'MANUFACTURING_ORDER_LINE'), state.visibleColumnsRM),
        cfFGLine: fgLineCFs,
        initialCfFGLine: fgLineCFs,
        visibleColumnsFG: CustomFieldHelpers.updateVisibleColumns(props.cfV2DocManufacturingOrder?.data?.document_line_custom_fields?.filter((item) => item.entity_name === 'MANUFACTURING_ORDER_FINISHED_GOOD'), state.visibleColumnsFG),
      };
    }
    return {
      ...state,
    };
  }

  getFgCosting(fgTenantProductId, fgQty) {
    const {
      trackWastage, finishedGoods, bomLines, extraCharges, costingMethod,
    } = this.state;
    const rawMaterials = bomLines[fgTenantProductId]?.filter((rm) => rm?.bom_id === rm?.parent_bom_id);
    const rm = rawMaterials?.filter((item) => item?.fg_tenant_product_id == fgTenantProductId);
    const charges = extraCharges[fgTenantProductId]?.filter((item) => item?.charge_amount > 0 && rm?.bom_id === rm?.parent_bom_id);
    const chg = charges?.filter((item) => item?.fg_tenant_product_id == fgTenantProductId);
    let totalRmCost = 0;
    let totalCharges = 0;
    for (let i = 0; i < rm?.length; i++) {
      const record = rm[i];
      const costPrice = costingMethod === '3_MONTH_WEIGHTED_AVERAGE'
        ? (record?.costPerUnit3Months || 0) : costingMethod === '6_MONTH_WEIGHTED_AVERAGE'
          ? (record?.costPerUnit6Months || 0) : (record?.costPerUnit || 0);
      totalRmCost += (costPrice * (record.estimatedUsage + (trackWastage ? Number(record?.wastage_quantity) : 0)));
    }
    for (let i = 0; i < chg?.length; i++) {
      const record = chg[i];
      totalCharges += Number(record?.charge_amount);
    }
    return {
      rmCostPerUnit: (totalRmCost / fgQty) || 0,
      chargePerUnit: (totalCharges / fgQty) || 0,
      totalCostPerUnit: ((totalRmCost / fgQty) + (totalCharges / fgQty)) || 0,
    };
  }

  createData = (dataItem, tenantSku, productData, fgTenantProductId, initialCfRMLine) => {
    const copyDataItem = dataItem;
    copyDataItem.asset1 = tenantSku?.product_info?.assets?.[0]?.url || '';
    copyDataItem.tenant_product_info = {
      product_sku_id: tenantSku?.product_sku_id,
      tenant_product_id: tenantSku?.tenant_product_id,
      tenant_id: tenantSku?.tenant_id,
      is_active: tenantSku?.is_active,
      alias_name: tenantSku?.alias_name,
      cost_price: tenantSku?.cost_price,
      uom_info: tenantSku?.uom_info,
      internal_sku_code: tenantSku?.product_info?.internal_sku_code,
      ref_product_code: tenantSku?.product_info?.ref_product_code,
    };
    copyDataItem.product_info = tenantSku?.product_info;
    copyDataItem.product_category_info = tenantSku?.product_category_info;
    copyDataItem.tenantProductId = tenantSku?.tenant_product_id;
    copyDataItem.currentStock = tenantSku?.available_qty || 0;
    copyDataItem.estimatedUsage = '';
    copyDataItem.uomId = tenantSku?.product_info?.uom_id || 0;
    copyDataItem.uomInfo = tenantSku?.product_info?.uom_info;
    copyDataItem.uomGroup = tenantSku?.group_id;
    copyDataItem.costPerUnit = tenantSku?.cost_price;
    copyDataItem.productSkuName = tenantSku?.product_info.product_sku_name;
    copyDataItem.product_sku_name = tenantSku?.product_info.product_sku_name;
    copyDataItem.product_sku_id = tenantSku?.product_info.product_sku_id;
    copyDataItem.tenantProductId = tenantSku?.tenant_product_id;
    copyDataItem.fg_tenant_product_id = fgTenantProductId;
    if (initialCfRMLine) copyDataItem.lineCustomFields = initialCfRMLine;

    return copyDataItem;
  }

  handleProductChangeRawMaterial = (tenantSku, key, productData, isMultiMode, callback) => {
    const { bomLines, finishedGoods, initialCfRMLine } = this.state;
    const fgTenantProductId = finishedGoods?.[0]?.tenant_product_info?.tenant_product_id;

    if (isMultiMode && Array.isArray(tenantSku)) {
      const copyData = [];
      tenantSku.forEach((tenantSkuData) => {
        const dataItem = {
          key: uuidv4(),
          parentKey: null,
          fg_tenant_product_id: fgTenantProductId,
          bom_id: finishedGoods?.[0]?.bom_id,
          parent_bom_id: finishedGoods?.[0]?.bom_id,
          lineCustomFields: initialCfRMLine,
        };
        const productSkuData = productData?.find((item) => item?.product_sku_id === tenantSkuData?.product_info?.product_sku_id);
        const copyDataItem = this.createData(dataItem, tenantSkuData, productSkuData, fgTenantProductId, initialCfRMLine);
        copyData.push(copyDataItem);
      });

      if (callback) {
        callback();
      }

      this.setState((prevState) => ({
        bomLines: {
          ...prevState.bomLines,
          [fgTenantProductId]: [
            ...prevState.bomLines[fgTenantProductId],
            ...copyData,
          ],
        },
      }));
    } else {
      const copyData = JSON.parse(JSON.stringify(bomLines[fgTenantProductId]));
      for (let i = 0; i < copyData.length; i++) {
        if (copyData[i].key === key) {
          const copyDataItem = this.createData(copyData[i], tenantSku, productData, fgTenantProductId, initialCfRMLine);
          copyData[i] = copyDataItem;
        }
      }
      this.setState({ bomLines: { [fgTenantProductId]: copyData } });
    }
  }

  handleProductChangeValueRawMaterial = (value, key) => {
    const { bomLines } = this.state;
    const copyData = JSON.parse(JSON.stringify(bomLines));
    for (let i = 0; i < copyData.length; i++) {
      if (copyData[i].key === key) {
        const copyDataItem = copyData[i];
        copyDataItem.product_sku_name = value;
        copyData[i] = copyDataItem;
      }
    }
    this.setState({ bomLines: copyData });
  }

  handleMultiProductChangeRM = (tenantSku, key, productData, callback) => {
    this.handleProductChangeRawMaterial(tenantSku, key, productData, true, callback);
  }

  handleProductChangeValueNewRawMaterial = (value, key) => {
    const { bomLines } = this.state;
    const copyBomLine = JSON.parse(JSON.stringify(bomLines));

    Object.keys(copyBomLine).forEach((productId) => copyBomLine[productId].forEach((bomLine) => {
      if (bomLine.key == key) {
        bomLine.product_sku_name = value;
      }
    }));
    this.setState({ bomLines: copyBomLine });
  }

  handleProductChangeNewRawMaterial = ({
    tenantSku, key, productData, currentRow, isMultiMode, callback,
  }) => {
    const { finishedGoods, bomLines, initialCfRMLine } = this.state;

    if (isMultiMode && Array.isArray(tenantSku)) {
      const bomId = currentRow.bom_id;
      const fgInfo = finishedGoods.find((bom) => bom.bom_id == bomId);
      const bomInfo = fgInfo?.bom_info;
      const fgTenantProductId = currentRow?.fg_tenant_product_id || fgInfo.tenant_product_info.tenant_product_id;

      const newBomLines = [];
      const newChildBomLine = [];

      tenantSku.forEach((tenantSkuData) => {
        const productSkuData = productData?.find((item) => item?.product_sku_id === tenantSkuData?.product_info?.product_sku_id);

        const bomLineId = uuidv4();

        const newLine = createDefaultBomLineData({
          bomId, bomLineId, bomInfo, fgInfo, fgTenantProductId, initialCfRMLine,
        });

        const bomData = createBomLineDataFromProductData({ tenantSku: tenantSkuData, productData: productSkuData, initialCfRMLine });

        if (!currentRow?.parentKey || !currentRow?.isChildBom) {
          newBomLines.push({ ...newLine, ...bomData });
        } else {
          const parentBomId = currentRow?.parent_bom_id;
          newLine.parent_bom_id = parentBomId;
          newLine.parentKey = currentRow?.parentKey;
          newLine.bom_line_parent_bom_id = parentBomId;
          newLine.isChildBom = true;

          const childBomLine = createDefaultChildBomLine({
            bomId, bomLineId, bomInfo, parentBomId,
          });

          const childBomLineData = createChildBomLineDataFromProductData({
            tenantSku: tenantSkuData, bomData,
          });
          newBomLines.push({ ...newLine, ...bomData });
          newChildBomLine.push({ ...childBomLine, ...childBomLineData });
        }
      });

      if (callback) callback();

      if (!currentRow?.parentKey || !currentRow?.isChildBom) {
        const copyData = JSON.parse(JSON.stringify(bomLines[fgTenantProductId]));
        copyData.push(...newBomLines);
        this.setState((prevData) => ({
          ...prevData,
          bomLines: {
            ...prevData.bomLines,
            [fgTenantProductId]: copyData,
          },
        }));
      } else {
        const copyData = JSON.parse(JSON.stringify(bomLines[fgTenantProductId]));
        const parentBomId = currentRow?.parent_bom_id;
        const parentData = copyData.find((line) => line.child_bom_id == parentBomId);
        const newCopyData = addNewRmRowAtChildBomLinesRecursively({
          bomLines: copyData, newLine: newBomLines, childBomLine: newChildBomLine, childBomId: parentBomId, parentBomLineId: parentData?.bom_line_id, isMultiMode: true,
        });

        this.setState((prevData) => ({
          ...prevData,
          bomLines: {
            ...prevData.bomLines,
            [fgTenantProductId]: newCopyData,
          },
        }));
      }
    } else {
      const bomData = createBomLineDataFromProductData({ tenantSku, productData, initialCfRMLine });

      const fgTenantProductId = currentRow.fg_tenant_product_id;
      let bomLinesCopy = JSON.parse(JSON.stringify(bomLines[fgTenantProductId]));

      if (!currentRow?.parentKey || !currentRow?.isChildBom) {
        for (let index = 0; index < bomLinesCopy.length; index++) {
          if (bomLinesCopy[index].key == key) {
            const bomLineItem = { ...bomLinesCopy[index], ...bomData };
            bomLinesCopy[index] = bomLineItem;
          }
        }

        this.setState((prevState) => ({
          ...prevState,
          bomLines: {
            ...prevState.bomLines,
            [fgTenantProductId]: bomLinesCopy,
          },
        }));
      } else {
        const childBomLineData = createChildBomLineDataFromProductData({
          tenantSku, bomData,
        });

        bomLinesCopy = addProductInfoOfRmRowAtChildBomLinesRecursively({ bomLines: bomLinesCopy, childBomLineData, bomLineId: currentRow?.bom_line_id });

        for (let index = 0; index < bomLinesCopy.length; index++) {
          if (bomLinesCopy[index].key == key) {
            const bomLineItem = { ...bomLinesCopy[index], ...bomData };
            bomLinesCopy[index] = bomLineItem;
          }
        }

        this.setState((prevState) => ({
          ...prevState,
          bomLines: {
            ...prevState.bomLines,
            [fgTenantProductId]: bomLinesCopy,
          },
        }));
      }
    }
  }

  handleMultiProductChangeNewRM = (tenantSku, key, productData, callback, record) => {
    this.handleProductChangeNewRawMaterial({
      tenantSku, key, productData, currentRow: record, isMultiMode: true, callback,
    });
  }

  handleProductChangeByProduct = (tenantSku, key, productData, record, isMultiMode, callback) => {
    const { bomByProducts, finishedGoods } = this.state;
    // const fgTenantProductId = finishedGoods?.[0]?.tenant_product_info?.tenant_product_id;
    const fgTenantProductId = record.fg_tenant_product_id;
    if (isMultiMode && Array.isArray(tenantSku)) {
      const copyData = [];
      tenantSku.forEach((tenantSkuData) => {
        const dataItem = {
          key: uuidv4(),
          parentKey: null,
          fg_tenant_product_id: fgTenantProductId,
        };
        const productSkuData = productData?.find((item) => item?.product_sku_id === tenantSkuData?.product_info?.product_sku_id);
        const copyDataItem = this.createData(dataItem, tenantSkuData, productSkuData, fgTenantProductId);
        copyData.push(copyDataItem);
      });

      if (callback) {
        callback();
      }

      this.setState((prevState) => ({
        bomByProducts: {
          ...prevState.bomByProducts,
          [fgTenantProductId]: getDataByEmptyRowAtLast([
            ...prevState.bomByProducts[fgTenantProductId],
            ...copyData,
          ]),
        },
      }));
    } else {
      const copyData = JSON.parse(JSON.stringify(bomByProducts[fgTenantProductId]));
      for (let i = 0; i < copyData.length; i++) {
        if (copyData[i].key === key) {
          const copyDataItem = this.createData(copyData[i], tenantSku, productData, fgTenantProductId);
          copyData[i] = copyDataItem;
        }
      }
      this.setState((prevState) => ({
        ...prevState,
        bomByProducts: {
          ...prevState.bomByProducts,
          [fgTenantProductId]: copyData,
        },
      }));
    }
  }

  handleProductChangeValueByProduct = (value, key) => {
    const { bomByProducts } = this.state;

    const copyBomByProducts = JSON.parse(JSON.stringify(bomByProducts));
    Object.keys(copyBomByProducts).forEach((productId) => copyBomByProducts[productId].forEach((byProductLine) => {
      if (byProductLine.key === key) {
        byProductLine.product_sku_name = value;
      }
    }));

    this.setState({
      bomByProducts: copyBomByProducts,
    });
  }

  handleMultiProductChangeBP = (tenantSku, key, productData, callback, record) => {
    this.handleProductChangeByProduct(tenantSku, key, productData, record, true, callback);
  }

  handleFgQuantityChange = (tenantProductId, value, key) => {
    const {
      finishedGoods,
      bomLines,
      bomByProducts,
      extraCharges,
      productionRoutes,
    } = this.state;

    // update FG line quantity
    const finishedGoodsCopy = JSON.parse(JSON.stringify(finishedGoods));
    const productionRoutesCopy = JSON.parse(JSON.stringify(productionRoutes));
    for (let i = 0; i < finishedGoods?.length; i++) {
      if (finishedGoods[i]?.key === key) {
        const fgItem = JSON.parse(JSON.stringify(finishedGoods[i]));
        fgItem.quantity = value;
        finishedGoodsCopy[i] = fgItem;
        break;
      }
    }

    if (productionRoutesCopy[tenantProductId]) {
      productionRoutesCopy[tenantProductId].br_lines =
        productionRoutesCopy?.[tenantProductId]?.br_lines?.map((line) => {
          const leadTimePerUnit = new Decimal(line?.lead_time_per_unit ?? 0);
          const fgQty = new Decimal(value ?? 0);
          line.lead_time_in_min = leadTimePerUnit.times(fgQty).toNumber();
          return line;
        });
    }
    this.setState({
      finishedGoods: finishedGoodsCopy,
      productionRoutes: productionRoutesCopy,
    });

    // Update RM line quantity
    const bomLinesCopy = JSON.parse(JSON.stringify(bomLines));
    const productBomLines = bomLinesCopy[tenantProductId];
    const updateRmQuantity = (currentKey, currentValue) => {
      for (let i = 0; i < productBomLines?.length; i++) {
        if (productBomLines[i]?.key === currentKey) {
          const precision = productBomLines[i]?.uom_info?.precision ?? 6;

          const currentVal = new Decimal(currentValue ?? 0);
          const wastagePercent = new Decimal(productBomLines[i]?.wastage_percentage ?? 0);
          const wastageQty = currentVal.times(wastagePercent.div(100));

          productBomLines[i].estimatedUsage = QUANTITY(
            currentVal.toNumber(),
            precision
          );
          productBomLines[i].wastage_quantity = QUANTITY(
            wastageQty.toNumber(),
            precision
          );
          productBomLines[i].fgQuantity = value;

          const updateValuesInCfLinesRm =
            CustomFieldHelpers.calculateValuesForDependsFields(
              productBomLines[i].lineCustomFields
            );
          productBomLines[i].lineCustomFields = updateValuesInCfLinesRm?.map(
            (item) => ({
              ...item,
              fieldValue:
                item?.fieldName === 'Quantity'
                  ? currentVal.toNumber()
                  : (item?.fieldType === 'NUMBERS'
                    ? Number(item?.fieldValue)
                    : item?.fieldValue),
            })
          );

          for (let j = 0; j < productBomLines?.length; j++) {
            if (productBomLines[j]?.parentKey === currentKey) {
              const parentQtyPerUnit = new Decimal(
                productBomLines[j]?.parentQuantityPerUnit ?? 0
              );
              updateRmQuantity(
                productBomLines[j]?.key,
                parentQtyPerUnit.times(currentVal.plus(wastageQty)).toNumber()
              );
            }
          }
        }
      }
    };
    for (let i = 0; i < productBomLines?.length; i++) {
      if (productBomLines[i]?.parentKey === null) {
        const qtyPerUnit = new Decimal(productBomLines[i]?.quantityPerUnit ?? 0);
        const fgQty = new Decimal(value ?? 0);
        updateRmQuantity(
          productBomLines[i]?.key,
          qtyPerUnit.times(fgQty).toNumber()
        );
      }
    }
    bomLinesCopy[tenantProductId] = productBomLines;

    // Update By Products line quantity
    const bomByProductsCopy = JSON.parse(JSON.stringify(bomByProducts));
    const productBomByProducts = bomByProductsCopy[tenantProductId];
    for (let i = 0; i < productBomByProducts?.length; i++) {
      const qtyPerUnit = new Decimal(productBomByProducts[i]?.quantityPerUnit ?? 0);
      const fgQty = new Decimal(value ?? 0);
      productBomByProducts[i].estimatedUsage = fgQty.times(qtyPerUnit).toNumber();
      productBomByProducts[i].fgQuantity = value;
    }
    bomByProductsCopy[tenantProductId] = productBomByProducts;

    // Update Extra Charges line quantity
    const extraChargesCopy = JSON.parse(JSON.stringify(extraCharges));
    const productExtraCharges = extraChargesCopy[tenantProductId];
    for (let i = 0; i < productExtraCharges?.length; i++) {
      if (!productExtraCharges[i]?.isForAdhocFG) {
        const fgQty = new Decimal(value ?? 0);
        const costPerUnit = new Decimal(productExtraCharges[i]?.costPerUnit ?? 0);
        productExtraCharges[i].charge_amount = fgQty.times(costPerUnit).toNumber();
        productExtraCharges[i].fgQuantity = value;
      }
    }
    extraChargesCopy[tenantProductId] = productExtraCharges;
    this.setState({
      finishedGoods: finishedGoodsCopy,
      bomLines: bomLinesCopy,
      bomByProducts: bomByProductsCopy,
      extraCharges: extraChargesCopy,
    });
  };

  handleRmQuantityChange = (fgTenantProductId, value, key, wastage) => {
    const { bomLines } = this.state;
    const bomLinesCopy = JSON.parse(JSON.stringify(bomLines));
    const productBomLines = bomLinesCopy[fgTenantProductId];

    const updateRmQuantity = (currentKey, currentValue, wastageValue) => {
      for (let i = 0; i < productBomLines?.length; i++) {
        if (productBomLines[i]?.key === currentKey) {
          const usage = new Decimal(currentValue ?? 0);
          const wastageQty = new Decimal(wastageValue ?? 0);

          productBomLines[i].estimatedUsage = usage.toNumber();
          productBomLines[i].wastage_quantity = wastageQty.toNumber();

          productBomLines[i].quantity = usage.plus(wastageQty).toNumber();

          productBomLines[i].lineCustomFields = productBomLines[i].lineCustomFields.map((field) => {
            if (field?.fieldName.toProperCase() === 'Quantity') {
              return {
                ...field,
                fieldValue: productBomLines[i].estimatedUsage,
              };
            }
            return { ...field };
          });
          for (let j = 0; j < productBomLines?.length; j++) {
            if (productBomLines[j]?.parentKey === currentKey) {
              const parentPerUnit = new Decimal(productBomLines[j]?.parentQuantityPerUnit ?? 0);
              const childValue = parentPerUnit.times(usage.plus(wastageQty));
              updateRmQuantity(productBomLines[j]?.key, childValue.toNumber());
            }
          }
        }
      }
    };
    updateRmQuantity(key, value, wastage);
    bomLinesCopy[fgTenantProductId] = productBomLines;
    this.setState({
      bomLines: bomLinesCopy,
    });
  };

  handleBpQuantityChange = (fgTenantProductId, value, key) => {
    const {
      bomByProducts,
    } = this.state;
    // Update BP line estimated quantity
    const bomByProductsCopy = JSON.parse(JSON.stringify(bomByProducts));
    const byProductsLines = bomByProductsCopy[fgTenantProductId];
    for (let i = 0; i < byProductsLines?.length; i++) {
      if (byProductsLines[i]?.key === key) {
        byProductsLines[i].estimatedUsage = value || '';
      }
    }
    bomByProductsCopy[fgTenantProductId] = byProductsLines;
    this.setState({
      bomByProducts: bomByProductsCopy,
    });
  }

  handleChangeRemarks = (tenantProductId, value, key) => {
    const {
      finishedGoods,
    } = this.state;
    // update FG line quantity
    const finishedGoodsCopy = JSON.parse(JSON.stringify(finishedGoods));
    for (let i = 0; i < finishedGoods?.length; i++) {
      if (finishedGoods[i]?.key === key) {
        const fgItem = JSON.parse(JSON.stringify(finishedGoods[i]));
        fgItem.bom_info.product_info.description = value;
        finishedGoodsCopy[i] = fgItem;
        break;
      }
    }
    this.setState({
      finishedGoods: finishedGoodsCopy,
    });
  }

  handleOtherChargesChange = (fgTenantProductId, chargeName, chargeValue, key) => {
    const {
      extraCharges,
    } = this.state;
    // Update OC line estimated charges
    const extraChargesCopy = JSON.parse(JSON.stringify(extraCharges));
    const extraChargesLines = extraChargesCopy[fgTenantProductId];
    for (let i = 0; i < extraChargesLines?.length; i++) {
      if (extraChargesLines[i]?.key === key) {
        if (chargeName) {
          extraChargesLines[i].charge_name = chargeName == 'empty' ? '' : chargeName || '';
        }
        if (chargeValue) {
          extraChargesLines[i].charge_amount = chargeValue == 'empty' ? '' : chargeValue;
        }
      }
    }
    extraChargesCopy[fgTenantProductId] = extraChargesLines;
    this.setState({
      extraCharges: extraChargesCopy,
    });
  }

  handleProductChangeValueOtherCharge = (value, key) => {
    const { bomByProducts } = this.state;
    const copyData = JSON.parse(JSON.stringify(bomByProducts));
    for (let i = 0; i < copyData.length; i++) {
      if (copyData[i].key === key) {
        const copyDataItem = copyData[i];
        copyDataItem.product_sku_name = value;
        copyData[i] = copyDataItem;
      }
    }
    this.setState({ bomByProducts: copyData });
  }

  handleRouteChange = (fgTenantProductId, key, fieldName, fieldValue) => {
    const {
      productionRoutes,
    } = this.state;
    const productionRoutesCopy = JSON.parse(JSON.stringify(productionRoutes));
    const productProductionRoutes = productionRoutesCopy[fgTenantProductId];
    if (['is_sequential', 'is_time_tracked'].includes(fieldName)) {
      productProductionRoutes[fieldName] = fieldValue;
    } else {
      for (let i = 0; i < productProductionRoutes?.br_lines?.length; i++) {
        if (productProductionRoutes?.br_lines[i]?.key === key) {
          productProductionRoutes.br_lines[i][fieldName] = fieldValue;
          if (fieldName === 'is_subcontractor') {
            productProductionRoutes.br_lines[i].machine_resource_group = null;
            productProductionRoutes.br_lines[i].subcontractor_seller_id = null;
          }
        }
      }
    }
    productionRoutesCopy[fgTenantProductId] = productProductionRoutes;
    this.setState({
      productionRoutes: productionRoutesCopy,
    });
  }

  fgLinesMapping = (data) => {
    const bomFgTenantProductIdMapping = {};
    for (let i = 0; i < data?.length; i++) {
      if (bomFgTenantProductIdMapping.hasOwnProperty(data[i]?.fg_tenant_product_id)) {
        bomFgTenantProductIdMapping[data[i]?.fg_tenant_product_id].push({
          ...data[i],
        });
      } else {
        bomFgTenantProductIdMapping[data[i]?.fg_tenant_product_id] = [{
          ...data[i],
        }];
      }
    }
    return bomFgTenantProductIdMapping;
  }

  handleDeleteFinishedGood = (key) => {
    const {
      finishedGoods, bomLines, bomByProducts, extraCharges, productionRoutes,
    } = this.state;
    const {
      match,
    } = this.props;
    const removedFg = finishedGoods.find((item) => item.key === key);
    const bomLinesCopy = JSON.parse(JSON.stringify(bomLines));
    const bomByProductsCopy = JSON.parse(JSON.stringify(bomByProducts));
    const extraChargesCopy = JSON.parse(JSON.stringify(extraCharges));
    const productionRoutesCopy = JSON.parse(JSON.stringify(productionRoutes));

    if (match?.params?.moId) {
      const flatBomLinesCopyData = Object.values(bomLinesCopy).flat();
      const uniqueRmSkuIds = [...new Set(flatBomLinesCopyData.map((item) => item.product_sku_id))];
      if (uniqueRmSkuIds?.length !== 1) {
        delete bomLinesCopy[removedFg?.tenant_product_info?.tenant_product_id];
      }
    } else {
      delete bomLinesCopy[removedFg?.tenant_product_info?.tenant_product_id];
    }

    delete bomByProductsCopy[removedFg?.tenant_product_info?.tenant_product_id];
    delete extraChargesCopy[removedFg?.tenant_product_info?.tenant_product_id];
    delete productionRoutesCopy[removedFg?.tenant_product_info?.tenant_product_id];
    const copyData = finishedGoods.filter((item) => item.key !== key);
    this.setState({
      finishedGoods: copyData, bomLines: bomLinesCopy, bomByProducts: bomByProductsCopy, extraCharges: extraChargesCopy, productionRoutes: productionRoutesCopy,
    });
    if (finishedGoods?.length === 1) {
      this.setState({
        finishedGoods: [],
      });
    }
  };

  checkChildren(bomLinesData) {
    const validBomLineIds = new Set(
      Object.values(bomLinesData)?.[0].map((item) => item?.bom_line_id),
    );

    const updatedBomLines = Object.keys(bomLinesData).reduce((acc, key) => {
      acc[key] = bomLinesData[key].map((obj) => {
        const hasValidChild = obj?.child_bom_lines?.some((child) => validBomLineIds.has(child?.bom_line_id));

        return {
          ...obj,
          child_bom_id: hasValidChild ? obj?.child_bom_id : null,
        };
      });
      return acc;
    }, {});
    return updatedBomLines;
  }

  handleDeleteRawMaterial = (productSkuId, rmKey) => {
    const { bomLines, finishedGoods } = this.state;
    const copyData = JSON.parse(JSON.stringify(bomLines));
    const flatData = Object.values(copyData).flat();
    const uniqueRmSkuIds = [...new Set(flatData.map((item) => item.product_sku_id))];
    if (finishedGoods?.length === 1) {
      if (flatData?.length > 1) {
        const copyBomLineArray = flatData.filter((item) => item.key !== rmKey);
        const copyData1 = this.fgLinesMapping(copyBomLineArray);
        this.setState({ bomLines: copyData1 });
      } else {
        notification.open({
          type: 'error',
          message: 'Atleast one raw material is required to create a finished good',
          duration: 4,
          placement: 'top',
        });
      }
    } else if (uniqueRmSkuIds?.length === 1) {
      notification.open({
        type: 'error',
        message: 'Atleast one raw material is required to create a finished good',
        duration: 4,
        placement: 'top',
      });
    } else {
      for (const key in copyData) {
        if (copyData.hasOwnProperty(key)) {
          const array = copyData[key];
          for (let i = array.length - 1; i >= 0; i--) {
            if (array[i].product_sku_id === productSkuId) {
              array.splice(i, 1);
            }
          }
        }
      }
      this.setState({ bomLines: copyData });
    }
  };

  deleteRmAllChildFormBomLinesRecursively = ({ childBomLines, bomLines }) => {
    if (!bomLines) {
      return [];
    }
    childBomLines.forEach((line) => {
      if (line?.child_bom_lines) {
        bomLines = this.deleteRmAllChildFormBomLinesRecursively({
          childBomLines: line?.child_bom_lines,
          bomLines,
        });
      }
      bomLines = bomLines.filter((bomLine) => bomLine.bom_line_id !== line.bom_line_id);
    });
    return bomLines;
  };

  deleteNewRowMaterialRecursively = ({ bomLines, childBomId, bomLineId }) => {
    bomLines.forEach((line) => {
      if (line?.child_bom_id == childBomId) {
        const childBomLines = line?.child_bom_lines?.filter((childBomLine) => childBomLine?.bom_line_id != bomLineId);
        if (!childBomLines.length) {
          delete line.child_bom_lines;
          line.child_bom_id = null;
        } else {
          line.child_bom_lines = childBomLines;
        }
      } else if (line?.child_bom_lines) {
        this.deleteNewRowMaterialRecursively({ bomLines: line?.child_bom_lines, childBomId, bomLineId });
      }
    });
    return bomLines;
  }

  handleDeleteNewRawMaterial = (productSkuId, rmKey, rm) => {
    const { bomLines, finishedGoods } = this.state;
    const fgTenantProductId = rm.fg_tenant_product_id;
    const copyData = JSON.parse(JSON.stringify(bomLines[fgTenantProductId]));
    // const flatData = Object.values(copyData).flat();
    const uniqueRmSkuIds = [...new Set(copyData.map((item) => item.product_sku_id))];
    if (uniqueRmSkuIds?.length === 1 && !copyData[0]?.isAdhocLine) {
      notification.open({
        type: 'error',
        message: 'Atleast one raw material is required to create a finished good',
        duration: 4,
        placement: 'top',
      });
    } else if (copyData?.length > 1 || (copyData?.length === 1 && copyData[0]?.isAdhocLine)) {
      let copyBomLineArray = copyData.filter((item) => item.key !== rmKey);
      // const copyData1 = this.fgLinesMapping(copyBomLineArray);
      if (rm?.child_bom_lines) {
        copyBomLineArray = this.deleteRmAllChildFormBomLinesRecursively({ childBomLines: rm?.child_bom_lines, bomLines: copyBomLineArray });
      }
      if (rm?.parentKey || rm?.isChildBom === true) {
        copyBomLineArray = this.deleteNewRowMaterialRecursively({ bomLines: copyBomLineArray, childBomId: rm?.parent_bom_id, bomLineId: rm?.bom_line_id });
      }
      this.setState((prevData) => ({
        bomLines: {
          ...prevData.bomLines,
          [fgTenantProductId]: copyBomLineArray,
        },
      }));
    } else {
      notification.open({
        type: 'error',
        message: 'Atleast one raw material is required to create a finished good',
        duration: 4,
        placement: 'top',
      });
    }
  };

  handleDeleteByProduct = (productSkuId, bPKey) => {
    const { bomByProducts, finishedGoods } = this.state;
    const copyData = JSON.parse(JSON.stringify(bomByProducts));
    const flatData = Object.values(copyData).flat();
    if (flatData?.length > 0) {
      const copyBomLineArray = flatData.filter((item) => item !== null).filter((item) => item.key !== bPKey);
      const copyData1 = this.fgLinesMapping(copyBomLineArray);
      this.setState({ bomByProducts: copyData1 });
    }
  };

  handleDeleteOtherCharges = (key) => {
    const { extraCharges } = this.state;
    const extraChargesLineArray = Object.values(extraCharges).flat();
    if (extraChargesLineArray?.length > 0) {
      const copyextraChargesLineArray = extraChargesLineArray.filter((item) => item.key !== key);
      const copyData = this.fgLinesMapping(copyextraChargesLineArray);
      this.setState({ extraCharges: copyData });
    }
  };

  handleDeleteProductionRoute = (fgTenantProductId, lineId) => {
    const { productionRoutes } = this.state;
    const productionRoutesLineArray = JSON.parse(JSON.stringify(productionRoutes));
    const fgProductionRoute = productionRoutesLineArray[fgTenantProductId];
    fgProductionRoute.br_lines = fgProductionRoute?.br_lines?.filter((item) => item?.bom_route_line_id !== lineId)?.map((item, index) => ({ ...item, step_number: index + 1 }));
    productionRoutesLineArray[fgTenantProductId] = fgProductionRoute;
    this.setState({ productionRoutes: productionRoutesLineArray });
  };

  addNewRowByProduct({ parentData }) {
    const { bomByProducts, finishedGoods } = this.state;
    const fgTenantProductId = parentData?.tenant_product_info?.tenant_product_id;
    const copyData = bomByProducts[fgTenantProductId]?.length ? JSON.parse(JSON.stringify(bomByProducts[fgTenantProductId])) : [];
    copyData.push({
      key: uuidv4(),
      bom_id: parentData?.bom_id,
      fg_tenant_product_id: fgTenantProductId,
      parentKey: null,
    });
    this.setState((prevData) => ({
      ...prevData,
      bomByProducts: {
        ...prevData.bomByProducts,
        [fgTenantProductId]: copyData,
      },
    }));
  }

  addNewRowRawMaterial() {
    const { bomLines, finishedGoods, initialCfRMLine } = this.state;
    const fgTenantProductId = finishedGoods?.[0]?.tenant_product_info?.tenant_product_id;
    const copyData = Object.values(bomLines).flat();
    copyData.push({
      key: uuidv4(),
      parentKey: null,
      fg_tenant_product_id: fgTenantProductId,
      bom_id: finishedGoods?.[0]?.bom_id,
      parent_bom_id: finishedGoods?.[0]?.bom_id,
      lineCustomFields: initialCfRMLine,
    });

    this.setState({ bomLines: { [fgTenantProductId]: copyData } });
  }

  addNewRowNewRawMaterial = ({ parentData, isChildBom, callback }) => {
    const { bomLines, finishedGoods, initialCfRMLine } = this.state;
    const bomId = parentData.bom_id;
    const bomLineId = uuidv4();
    const fgInfo = finishedGoods.find((bom) => bom.bom_id == bomId);
    const bomInfo = fgInfo?.bom_info;
    const fgTenantProductId = parentData?.fg_tenant_product_id || fgInfo.tenant_product_info.tenant_product_id;
    let copyData = JSON.parse(JSON.stringify(bomLines[fgTenantProductId]));

    const newLine = createDefaultBomLineData({
      bomId, bomLineId, bomInfo, fgInfo, fgTenantProductId, initialCfRMLine,
    });

    if (!parentData?.parent_bom_id) {
      if (copyData && Array.isArray(copyData)) {
        copyData.push(newLine);
      } else {
        copyData = [newLine];
      }

      this.setState((prevData) => ({
        ...prevData,
        bomLines: {
          ...prevData.bomLines,
          [fgTenantProductId]: copyData,
        },
      }));
    } else {
      const parentBomId = parentData?.child_bom_id ?? uuidv4();
      newLine.parent_bom_id = parentBomId;
      newLine.parentKey = parentData?.key;
      newLine.bom_line_parent_bom_id = parentBomId;
      newLine.isChildBom = true;

      const childBomLine = createDefaultChildBomLine({
        bomId, bomLineId, bomInfo, parentBomId,
      });

      const newCopyData = addNewRmRowAtChildBomLinesRecursively({
        bomLines: copyData, newLine, childBomLine, childBomId: parentBomId, parentBomLineId: parentData?.bom_line_id,
      });

      if (callback) callback(parentData.key);

      this.setState((prevData) => ({
        ...prevData,
        bomLines: {
          ...prevData.bomLines,
          [fgTenantProductId]: newCopyData,
        },
      }));
    }
  }

  addNewProductionRoute() {
    const { productionRoutes, finishedGoods } = this.state;
    const { user } = this.props;
    const fgTenantProductId = finishedGoods?.[0]?.tenant_product_info?.tenant_product_id;
    productionRoutes[fgTenantProductId]?.br_lines?.push({
      key: uuidv4(),
      fg_tenant_product_id: fgTenantProductId,
      step_number: productionRoutes[fgTenantProductId]?.br_lines?.length + 1,
      process_name: `Step ${productionRoutes[fgTenantProductId]?.br_lines?.length + 1}`,
      org_id: user?.tenant_info?.org_id,
    });
    this.setState({ productionRoutes });
  }

  addNewRowFinishedGood(isAdhocLine) {
    const { finishedGoods, initialCfFGLine } = this.state;
    const copyData = JSON.parse(JSON.stringify(finishedGoods));

    const newLine = {
      key: uuidv4(),
      asset1: '',
      product_sku_name: '',
      quantity: '',
      unitPrice: '',
      taxId: '',
      lot: '',
      lineCustomFields: initialCfFGLine,
    };
    if (isAdhocLine) newLine.isAdhocLine = true;
    copyData.push(newLine);
    this.setState({ finishedGoods: copyData });
  }

  isDataValidFG() {
    const { finishedGoods } = this.state;
    let isDataValid = true;
    if (finishedGoods?.length) {
      for (let i = 0; i < finishedGoods?.length; i++) {
        if (!finishedGoods[i]?.quantity || finishedGoods[i]?.quantity <= 0 || !CustomFieldHelpers.isCfValid(finishedGoods[i]?.lineCustomFields)) {
          isDataValid = false;
          break;
        }
      }
    } else {
      notification.open({
        type: 'error',
        message: 'Please ensure that at least one Finished Good is provided',
        duration: 4,
        placement: 'top',
      });
      isDataValid = false;
    }
    return isDataValid;
  }

  isDataValidRM() {
    const { bomLines, finishedGoods } = this.state;
    let isDataValid2 = true;

    Object.keys(bomLines).forEach((fgTenantProductId) => {
      const bomLineArray = bomLines[fgTenantProductId];

      if (bomLineArray.length) {
        for (let i = 0; i < bomLineArray?.length; i++) {
          const bomLine = bomLineArray[i];
          const productSkuName = bomLine?.productSkuName;
          if (!bomLine?.estimatedUsage || Number(bomLine?.estimatedUsage) <= 0 || !CustomFieldHelpers.isCfValid(bomLine?.lineCustomFields)) {
            isDataValid2 = false;

            notification.error({
              message: `The product SKU "${productSkuName}" has zero Estimated Usage or has a missing Mandatory Field Value. Please update.`,
              duration: 4,
              placement: 'top',
            });

            break;
          }
        }
      } else {
        const fgInfo = finishedGoods?.find((fg) => fg?.tenant_product_info?.tenant_product_id == fgTenantProductId);
        const finishedGoodType = fgInfo?.isAdhocLine;
        if (!(finishedGoodType)) {
          isDataValid2 = false;

          notification.open({
            type: 'error',
            message: `At least one raw material is required to create finished good ${fgInfo?.tenant_product_info?.internal_sku_code}`,
            duration: 4,
            placement: 'top',
          });
        }
      }
    });

    return isDataValid2;
  }

  isDataValidBP() {
    const { bomByProducts } = this.state;
    const byProductsLineArray = Object.values(bomByProducts).flat();
    let isDataValid3 = true;
    if (byProductsLineArray?.filter((item) => item?.tenantProductId)?.length < 1) {
      return true;
    }
    for (let i = 0; i < byProductsLineArray?.length; i++) {
      if (!byProductsLineArray[i]?.estimatedUsage || Number(byProductsLineArray[i]?.estimatedUsage) <= 0) {
        isDataValid3 = false;
        break;
      }
    }

    return isDataValid3;
  }

  isDataValidOC() {
    const { extraCharges } = this.state;
    const extraChargesLineArray = Object.values(extraCharges).flat();
    let isDataValid4 = true;
    if (extraChargesLineArray?.filter((item) => item?.tenantProductId)?.length < 1) {
      return true;
    }
    for (let i = 0; i < extraChargesLineArray?.length; i++) {
      if (!extraChargesLineArray[i]?.charge_amount.toString()) {
        isDataValid4 = false;
        break;
      }
    }

    return isDataValid4;
  }

  isDataErrorFree() {
    const { docLevelError, lineLevelError } = this.getManufacturingOrderErrors();
    if (docLevelError?.length > 0 || lineLevelError?.length > 0) {
      return false;
    }
    return true;
  }

  getAllAncestorProductIds(baseBomLine, bomLines) {
    if (!baseBomLine?.parentKey) {
      return [...baseBomLine?.tenant_product_info?.tenant_product_id ? [baseBomLine?.tenant_product_info?.tenant_product_id?.toString()] : []];
    }
    return [...baseBomLine?.tenant_product_info?.tenant_product_id ? [baseBomLine?.tenant_product_info?.tenant_product_id?.toString()] : [], ...this.getAllAncestorProductIds(bomLines.find((line) => line.child_bom_id === baseBomLine?.parent_bom_id), bomLines)];
  }

  getExcludedProduct(fgTenantProductId, parentBomId, bomId) {
    const excludedProducts = [];
    const { bomLines, bomByProducts, finishedGoods } = this.state;
    // const fgTenantProductId = finishedGoods?.[0]?.tenant_product_info?.tenant_product_id;

    // exclude all the finished goods
    for (let i = 0; i < finishedGoods?.length; i++) {
      if (finishedGoods[i]?.tenant_product_info?.tenant_product_id) excludedProducts.push(finishedGoods[i]?.tenant_product_info?.tenant_product_id?.toString());
    }

    // this condition will used when bomline is sfg so it will exclude all the products of its ancestors as well
    if (fgTenantProductId && parentBomId && parentBomId !== bomId) {
      for (let i = 0; i < bomByProducts[fgTenantProductId]?.length; i++) {
        if (bomByProducts[fgTenantProductId]?.[i]?.tenant_product_info?.tenant_product_id) excludedProducts.push(bomByProducts[fgTenantProductId]?.[i]?.tenant_product_info?.tenant_product_id?.toString());
      }
      for (let i = 0; i < bomLines[fgTenantProductId]?.length; i++) {
        if (bomLines[fgTenantProductId]?.[i]?.child_bom_id === parentBomId) {
          // if (bomLines[fgTenantProductId]?.[i]?.tenant_product_info?.tenant_product_id) excludedProducts.push(bomLines[fgTenantProductId]?.[i]?.tenant_product_info?.tenant_product_id?.toString());
          const ancestorProductIds = this.getAllAncestorProductIds(bomLines[fgTenantProductId]?.[i], bomLines[fgTenantProductId]);
          excludedProducts.push(...ancestorProductIds);
        } else if (bomLines[fgTenantProductId]?.[i]?.parent_bom_id === parentBomId) {
          if (bomLines[fgTenantProductId]?.[i]?.tenant_product_info?.tenant_product_id) excludedProducts.push(bomLines[fgTenantProductId]?.[i]?.tenant_product_info?.tenant_product_id?.toString());
        }
      }
      // this condition will used when bomline is rm for fg it will exclude all the rm of that fg
    } else if (fgTenantProductId && parentBomId && parentBomId === bomId) {
      for (let i = 0; i < bomByProducts[fgTenantProductId]?.length; i++) {
        if (bomByProducts[fgTenantProductId]?.[i]?.tenant_product_info?.tenant_product_id) excludedProducts.push(bomByProducts[fgTenantProductId]?.[i]?.tenant_product_info?.tenant_product_id?.toString());
      }
      for (let i = 0; i < bomLines[fgTenantProductId]?.length; i++) {
        if (bomLines[fgTenantProductId]?.[i]?.parent_bom_id === parentBomId && bomLines[fgTenantProductId]?.[i]?.parent_bom_id === bomLines[fgTenantProductId]?.[i]?.bom_id) {
          if (bomLines[fgTenantProductId]?.[i]?.tenant_product_info?.tenant_product_id) excludedProducts.push(bomLines[fgTenantProductId]?.[i]?.tenant_product_info?.tenant_product_id?.toString());
        }
      }
      // this condition will exclude all the product that comes under that fg including all rm and by products
    } else if (fgTenantProductId) {
      for (let i = 0; i < bomByProducts[fgTenantProductId]?.length; i++) {
        if (bomByProducts[fgTenantProductId]?.[i]?.tenant_product_info?.tenant_product_id) excludedProducts.push(bomByProducts[fgTenantProductId]?.[i]?.tenant_product_info?.tenant_product_id?.toString());
      }
      for (let i = 0; i < bomLines[fgTenantProductId]?.length; i++) {
        if (bomLines[fgTenantProductId]?.[i]?.tenant_product_info?.tenant_product_id) excludedProducts.push(bomLines[fgTenantProductId]?.[i]?.tenant_product_info?.tenant_product_id?.toString());
      }
      // this will exclude all the product including all rm and by products
    } else {
      finishedGoods.forEach((fg) => {
        const tempFgTenantProductId = fg?.tenant_product_info?.tenant_product_id;

        for (let i = 0; i < bomByProducts[tempFgTenantProductId]?.length; i++) {
          if (bomByProducts[tempFgTenantProductId]?.[i]?.tenant_product_info?.tenant_product_id) excludedProducts.push(bomByProducts[tempFgTenantProductId]?.[i]?.tenant_product_info?.tenant_product_id?.toString());
        }
        for (let i = 0; i < bomLines[tempFgTenantProductId]?.length; i++) {
          if (bomLines[tempFgTenantProductId]?.[i]?.tenant_product_info?.tenant_product_id) excludedProducts.push(bomLines[tempFgTenantProductId]?.[i]?.tenant_product_info?.tenant_product_id?.toString());
        }
      });
    }
    return excludedProducts;
  }

  getBomLinesWithKeys = (bom, key) => {
    const rawMaterialLines = [];

    const getBomRawMaterials = (bomLines, parentQuantity, parentKey, isChildBom) => {
      if (!Array.isArray(bomLines) || bomLines.length === 0) return rawMaterialLines;

      for (const item of bomLines) {
        const currentParentkey = uuidv4();

        const bomLineQty = new Decimal(item?.bom_line_quantity ?? 0);
        const uomRatio = new Decimal(item?.uom_info?.ratio ?? 1);
        const tenantUomRatio = new Decimal(item?.tenant_product_info?.uom_info?.ratio ?? 1);
        const parentQty = new Decimal(parentQuantity ?? 1);
        const bomQty = new Decimal(item?.bom_quantity ?? 1);

        const baseQty = bomLineQty.mul(uomRatio).div(tenantUomRatio);

        const parentQuantityPerUnit = parentQty.isZero() ? new Decimal(0) : baseQty.div(parentQty);

        const estimatedUsage = bomQty.isZero() ? new Decimal(0) : baseQty.div(bomQty).mul(bomQty);

        const wastageQty = estimatedUsage.mul(new Decimal(item?.wastage_percentage || 0).div(100));

        rawMaterialLines.push({
          ...item,
          key: currentParentkey,
          parentKey,
          productSkuName: item?.product_info?.product_sku_name,
          product_category_info: item?.product_info?.product_category_info,
          bomLineId: item?.bom_line_id,
          tenantProductId: item?.tenant_product_info?.tenant_product_id,
          tenant_product_info: {
            ...item?.tenant_product_info,
            internal_sku_code: item?.product_info?.internal_sku_code,
          },
          currentStock: item?.tenant_product_info?.on_hand_qty,
          costPerUnit: item?.tenant_product_info?.cost_price,
          costPerUnit3Months: item?.tenant_product_info?.weighted_avg_cost_of_last_3_months,
          costPerUnit6Months: item?.tenant_product_info?.weighted_avg_cost_of_last_6_months,
          quantityPerUnit: bomQty.isZero() ? 0 : baseQty.div(bomQty),
          parentQuantityPerUnit: parentQuantityPerUnit,
          estimatedUsage: estimatedUsage,
          wastage_quantity: wastageQty,
          uomId: item?.tenant_product_info?.uom_info?.uom_id,
          uomInfo: item?.tenant_product_info?.uom_info,
          uom_info: item?.tenant_product_info?.uom_info,
          fgQuantity: bom?.quantity,
          bom_id: bom?.bom_id,
          fg_tenant_product_id: bom?.tenant_product_info?.tenant_product_id,
          child_bom_id: item?.child_bom_id,
          parent_bom_id: item?.parent_bom_id,
          bom_line_parent_bom_id: item?.parent_bom_id,
          lineCustomFields: FormHelpers.lineSystemFieldValue(this.state.initialCfRMLine, item?.bom_line_custom_fields)?.map((i) => ({
            ...i,
            fieldValue:
              i?.fieldName === 'Quantity'
                ? estimatedUsage.toNumber()
                : (i?.fieldType === 'NUMBERS'
                  ? Number(i?.fieldValue)
                  : i?.fieldValue),
          })),
          isChildBom,
        });
        getBomRawMaterials(item?.child_bom_lines, estimatedUsage, currentParentkey, true);
      }
      return rawMaterialLines;
    };

    return getBomRawMaterials(bom?.bom_lines_first_level_exploded_view, new Decimal(bom?.bom_quantity ?? 1), key);
  };
  onSelectBom = (key, value) => {
    const {
      finishedGoods, bomLines, bomByProducts, extraCharges, tenantDepartmentId, rmTenantDepartmentId, productionRoutes, selectedTenant, moUsers, restrictionCheck, visibleColumnsRM, visibleColumnsFG, initialCfFGLine,
    } = this.state;
    const { getBOMById, user, cfV2DocManufacturingOrder } = this.props;
    let fgItemId = '';
    getBOMById(user?.tenant_info?.org_id, selectedTenant, value?.bom_id, rmTenantDepartmentId, 'first_level', (bom) => {
      fgItemId = bom?.tenant_product_info?.tenant_product_id;
      const finishedGoodsCopy = JSON.parse(JSON.stringify(finishedGoods));
      for (let i = 0; i < finishedGoods?.length; i++) {
        if (finishedGoods[i]?.key === key) {
          const fgItem = JSON.parse(JSON.stringify(finishedGoods[i]));
          fgItem.bom_id = bom?.bom_id;
          fgItem.tenant_product_info = {
            ...bom?.tenant_product_info,
            internal_sku_code: bom?.internal_sku_code,
          };
          fgItem.quantity = bom?.bom_quantity;
          fgItem.flexible_consumption = bom?.flexible_consumption;
          fgItem.bom_info = bom;
          fgItem.bom_unit_rm_cost = bom?.item_unit_cost_with_wastage;
          fgItem.bom_unit_charges = bom?.bom_extra_charges_per_unit;
          fgItem.bom_unit_total = Number(bom?.item_unit_cost_with_wastage) + Number(bom?.bom_extra_charges_per_unit);
          fgItem.bom_cost = bom?.bom_cost;
          fgItem.lineCustomFields = FormHelpers.lineSystemFieldValue(initialCfFGLine, bom?.custom_fields)?.map((i) => ({
            ...i,
            fieldValue: (i?.fieldName === 'Quantity' ? Number(bom?.bom_quantity) : (i?.fieldType === 'NUMBERS' ? Number(i?.fieldValue) : i?.fieldValue)),
          }));
          finishedGoodsCopy[i] = fgItem;
          this.setState({
            bomLines: {
              ...bomLines,
              [bom?.tenant_product_info?.tenant_product_id]: this.getBomLinesWithKeys(bom, null),
            },
            bomByProducts: {
              ...bomByProducts,
              [bom?.tenant_product_info?.tenant_product_id]: bom.bom_by_products?.map((item) => {
                const bpQty = new Decimal(item?.bom_bp_quantity ?? 0);
                const uomRatio = new Decimal(item?.uom_info?.ratio ?? 1);
                const tenantUomRatio = new Decimal(item?.tenant_product_info?.uom_info?.ratio ?? 1);
                const bomQty = new Decimal(item?.bom_quantity ?? 1);

                const normalizedQty = bpQty.times(uomRatio).div(tenantUomRatio);

                const quantityPerUnit = normalizedQty.div(bomQty);

                const estimatedUsage = quantityPerUnit.times(bomQty);

                return {
                  ...item,
                  quantityPerUnit: quantityPerUnit.toNumber(),
                  estimatedUsage: estimatedUsage.toNumber(),
                  tenantProductId: item?.tenant_product_info?.tenant_product_id,
                  tenant_product_info: {
                    ...item?.tenant_product_info,
                    internal_sku_code: item?.product_info?.internal_sku_code,
                  },
                  bomByProductId: item?.bom_by_product_id,
                  asset1: item?.product_info?.assets?.[0]?.url,
                  isPerishable: item?.product_info?.is_perishable,
                  productSkuName: item?.product_info?.product_sku_name,
                  product_category_info: item?.product_info?.product_category_info,
                  uomId: item?.tenant_product_info?.uom_info?.uom_id,
                  uomInfo: item?.tenant_product_info?.uom_info,
                  uom_info: item?.tenant_product_info?.uom_info,
                  fgQuantity: new Decimal(item?.bom_quantity ?? 0).toNumber(),
                  bom_id: bom?.bom_id,
                  fg_tenant_product_id: bom?.tenant_product_info?.tenant_product_id,
                  key: uuidv4(),
                  parentKey: null,
                };
              }),
            },
            extraCharges: {
              ...extraCharges,
              [bom?.tenant_product_info?.tenant_product_id]: bom?.extra_charges?.map((item) => ({
                ...item,
                costPerUnit: (item.charge_amount / bom?.bom_quantity)?.toFixed(DEFAULT_CUR_ROUND_OFF),
                key: uuidv4(),
                fgQuantity: bom?.bom_quantity,
                bom_id: bom?.bom_id,
                fg_tenant_product_id: bom?.tenant_product_info?.tenant_product_id,
                charge_name: item?.message || item?.charge_name,
                charge_amount: 0,
              })),
            },
            productionRoutes: {
              ...productionRoutes,
              [bom?.tenant_product_info?.tenant_product_id]: {
                ...bom?.bom_production_route,
                key: uuidv4(),
                bom_id: bom?.bom_id,
                fg_tenant_product_id: bom?.tenant_product_info?.tenant_product_id,
                br_lines: bom?.bom_production_route?.br_lines?.map((rec) => ({
                  ...rec,
                  is_subcontractor: !!rec?.subcontractor_seller_id,
                  machine_resource_group: rec?.machine_resource_group?.resource_group_id,
                  equipments_resource_groups: rec?.equipments_resource_groups?.map((item) => item?.resource_group_id),
                  other_resource_groups: rec?.other_resource_groups?.map((item) => item?.resource_group_id),
                  fixed_charge: rec?.route_line_charges?.find((item) => item?.charge_type === 'fixed')?.charge_amount,
                  unit_charge: rec?.route_line_charges?.find((item) => item?.charge_type === 'unit')?.charge_per_unit,
                  fg_tenant_product_id: bom?.tenant_product_info?.tenant_product_id,
                  lead_time_per_unit: (rec?.lead_time_in_min || 0) / bom?.bom_quantity,
                  key: uuidv4(),
                })),
                tenant_product_info: bom?.tenant_product_info,
                productSkuName: bom?.tenant_product_info?.alias_name,
              },
            },
            finishedGoods: finishedGoodsCopy,
            moUsers: [...new Set([...moUsers, ...(bom?.bom_users?.length > 0 ? bom.bom_users : [])])],
            restrictMoUser: !!((restrictionCheck && bom?.bom_users?.length > 0)),
            isIssueSfgEnabled: finishedGoods?.length > 1 ? false : bom?.is_issue_sfg_enabled,
            visibleColumnsRM: CustomFieldHelpers.updateVisibleColumns(cfV2DocManufacturingOrder?.data?.document_line_custom_fields?.filter((item) => item.entity_name === 'MANUFACTURING_ORDER_LINE'), visibleColumnsRM),
            cfRMLine: CustomFieldHelpers.getDocumentCf(cfV2DocManufacturingOrder?.data?.document_line_custom_fields?.filter((item) => item.entity_name === 'MANUFACTURING_ORDER_LINE'), bom?.bom_lines?.[0]?.bom_line_custom_fields || []),
            visibleColumnsFG: CustomFieldHelpers.updateVisibleColumns(cfV2DocManufacturingOrder?.data?.document_line_custom_fields?.filter((item) => item.entity_name === 'MANUFACTURING_ORDER_FINISHED_GOOD'), visibleColumnsFG),
            cfFGLine: CustomFieldHelpers.getDocumentCf(cfV2DocManufacturingOrder?.data?.document_line_custom_fields?.filter((item) => item.entity_name === 'MANUFACTURING_ORDER_FINISHED_GOOD'), bom?.custom_fields || []),
          }, () => {
            this.handleFgQuantityChange(fgItemId, bom?.bom_quantity, key);
          });
        }
      }
    });
  }

  handleProductChangeValue = (value, key) => {
    const { finishedGoods } = this.state;
    const copyData = JSON.parse(JSON.stringify(finishedGoods));
    for (let i = 0; i < copyData?.length; i++) {
      if (copyData[i].key === key) {
        const copyDataItem = copyData[i];
        copyDataItem.product_sku_name = value;
        copyData[i] = copyDataItem;
      }
    }
    this.setState({ finishedGoods: copyData });
  };

  handleProductChangeFG = (tenantSku, key, productData, isMultiMode, callback) => {
    const {
      finishedGoods, bomLines, bomByProducts, extraCharges, tenantDepartmentId, rmTenantDepartmentId, productionRoutes, selectedTenant, moUsers, restrictionCheck, visibleColumnsRM, visibleColumnsFG, initialCfFGLine,
    } = this.state;
    const {
      getBOMById, user, cfV2DocManufacturingOrder, getProductByIdLoading, getProductById, org, customMessageBillOfMaterial,
    } = this.props;
    let fgItemId = '';

    if (isMultiMode && Array.isArray(tenantSku)) {
      const newFinishedGoods = [];
      const newBomLines = {};
      const newProductionRoutes = {};
      const newExtraCharges = {};

      tenantSku.forEach((tenantSkuData) => {
        const productSkuData = productData?.find((item) => item?.product_sku_id === tenantSkuData?.product_info?.product_sku_id);

        const newLine = {
          key: uuidv4(),
          asset1: '',
          product_sku_name: '',
          quantity: '',
          unitPrice: '',
          taxId: '',
          lot: '',
          lineCustomFields: initialCfFGLine,
          isAdhocLine: true,
        };

        const bom = createBomDataFromProductData({
          tenantSku: tenantSkuData,
          productData: productSkuData,
          org,
          customMessageBillOfMaterial,
        });
        const fgItem = newLine;
        fgItem.bom_id = bom?.bom_id;
        fgItem.tenant_product_info = {
          ...bom?.tenant_product_info,
          internal_sku_code: bom?.internal_sku_code,
        };
        fgItem.quantity = bom?.bom_quantity;
        fgItem.flexible_consumption = bom?.flexible_consumption;
        fgItem.bom_info = bom;
        fgItem.bom_unit_rm_cost = bom?.item_unit_cost_with_wastage;
        fgItem.bom_unit_charges = bom?.bom_extra_charges_per_unit;
        fgItem.bom_unit_total = Number(bom?.item_unit_cost_with_wastage) + Number(bom?.bom_extra_charges_per_unit);
        fgItem.bom_cost = bom?.bom_cost;
        fgItem.lineCustomFields = FormHelpers.lineSystemFieldValue(initialCfFGLine, bom?.custom_fields)?.map((index) => ({
          ...index,
          fieldValue: (index?.fieldName === 'Quantity' ? Number(bom?.bom_quantity) : (index?.fieldType === 'NUMBERS' ? Number(index?.fieldValue) : index?.fieldValue)),
        }));

        newFinishedGoods.push(fgItem);
        newBomLines[bom?.tenant_product_info?.tenant_product_id] = [];
        newProductionRoutes[bom?.tenant_product_info?.tenant_product_id] = {
          ...bom?.bom_production_route,
          key: uuidv4(),
          bom_id: bom?.bom_id,
          fg_tenant_product_id: bom?.tenant_product_info?.tenant_product_id,
          br_lines: [],
          tenant_product_info: bom?.tenant_product_info,
          productSkuName: bom?.tenant_product_info?.alias_name,
        };
        newExtraCharges[bom?.tenant_product_info?.tenant_product_id] = bom?.extra_charges?.map((item) => ({
          ...item,
          key: uuidv4(),
          fgQuantity: bom?.bom_quantity,
          bom_id: bom?.bom_id,
          fg_tenant_product_id: bom?.tenant_product_info?.tenant_product_id,
          isForAdhocFG: true,
          charge_name: item?.message || item?.charge_name,
          charge_amount: 0,
        }));
      });

      this.setState((prevState) => ({
        bomLines: {
          ...prevState.bomLines,
          ...newBomLines,
        },
        bomByProducts: {
          ...prevState.bomByProducts,
          ...newBomLines,
        },
        extraCharges: {
          ...prevState.extraCharges,
          ...newExtraCharges,
        },
        productionRoutes: {
          ...prevState.productionRoutes,
          ...newProductionRoutes,
        },
        finishedGoods: [...prevState.finishedGoods, ...newFinishedGoods],
        isIssueSfgEnabled: finishedGoods?.length > 1 ? false : prevState?.isIssueSfgEnabled,
        visibleColumnsRM: CustomFieldHelpers.updateVisibleColumns(cfV2DocManufacturingOrder?.data?.document_line_custom_fields?.filter((item) => item.entity_name === 'MANUFACTURING_ORDER_LINE'), visibleColumnsRM),
        cfRMLine: CustomFieldHelpers.getDocumentCf(cfV2DocManufacturingOrder?.data?.document_line_custom_fields?.filter((item) => item.entity_name === 'MANUFACTURING_ORDER_LINE'), []),
        visibleColumnsFG: CustomFieldHelpers.updateVisibleColumns(cfV2DocManufacturingOrder?.data?.document_line_custom_fields?.filter((item) => item.entity_name === 'MANUFACTURING_ORDER_FINISHED_GOOD'), visibleColumnsFG),
        cfFGLine: CustomFieldHelpers.getDocumentCf(cfV2DocManufacturingOrder?.data?.document_line_custom_fields?.filter((item) => item.entity_name === 'MANUFACTURING_ORDER_FINISHED_GOOD'), []),
      }), () => {
        if (callback) callback();
      });
    } else {
      const bom = createBomDataFromProductData({
        tenantSku,
        productData,
        org,
        customMessageBillOfMaterial,
      });

      fgItemId = bom?.tenant_product_info?.tenant_product_id;
      const finishedGoodsCopy = JSON.parse(JSON.stringify(finishedGoods));
      for (let i = 0; i < finishedGoods?.length; i++) {
        if (finishedGoods[i]?.key === key) {
          const fgItem = JSON.parse(JSON.stringify(finishedGoods[i]));
          fgItem.bom_id = bom?.bom_id;
          fgItem.tenant_product_info = {
            ...bom?.tenant_product_info,
            internal_sku_code: bom?.internal_sku_code,
          };
          fgItem.quantity = bom?.bom_quantity;
          fgItem.flexible_consumption = bom?.flexible_consumption;
          fgItem.bom_info = bom;
          fgItem.bom_unit_rm_cost = bom?.item_unit_cost_with_wastage;
          fgItem.bom_unit_charges = bom?.bom_extra_charges_per_unit;
          fgItem.bom_unit_total = Number(bom?.item_unit_cost_with_wastage) + Number(bom?.bom_extra_charges_per_unit);
          fgItem.bom_cost = bom?.bom_cost;
          fgItem.lineCustomFields = FormHelpers.lineSystemFieldValue(initialCfFGLine, bom?.custom_fields)?.map((index) => ({
            ...index,
            fieldValue: (index?.fieldName === 'Quantity' ? Number(bom?.bom_quantity) : (index?.fieldType === 'NUMBERS' ? Number(index?.fieldValue) : index?.fieldValue)),
          }));
          finishedGoodsCopy[i] = fgItem;

          this.setState((prevState) => ({
            bomLines: {
              ...bomLines,
              [bom?.tenant_product_info?.tenant_product_id]: [],
            },
            bomByProducts: {
              ...bomByProducts,
              [bom?.tenant_product_info?.tenant_product_id]: [],
            },
            extraCharges: {
              ...extraCharges,
              [bom?.tenant_product_info?.tenant_product_id]: bom?.extra_charges?.map((item) => ({
                ...item,
                key: uuidv4(),
                fgQuantity: bom?.bom_quantity,
                bom_id: bom?.bom_id,
                fg_tenant_product_id: bom?.tenant_product_info?.tenant_product_id,
                isForAdhocFG: true,
                charge_name: item?.message || item?.charge_name,
                charge_amount: 0,
              })),
            },
            productionRoutes: {
              ...productionRoutes,
              [bom?.tenant_product_info?.tenant_product_id]: {
                ...bom?.bom_production_route,
                key: uuidv4(),
                bom_id: bom?.bom_id,
                fg_tenant_product_id: bom?.tenant_product_info?.tenant_product_id,
                br_lines: [],
                tenant_product_info: bom?.tenant_product_info,
                productSkuName: bom?.tenant_product_info?.alias_name,
              },
            },
            finishedGoods: finishedGoodsCopy,
            isIssueSfgEnabled: finishedGoods?.length > 1 ? false : prevState?.isIssueSfgEnabled,
            visibleColumnsRM: CustomFieldHelpers.updateVisibleColumns(cfV2DocManufacturingOrder?.data?.document_line_custom_fields?.filter((item) => item.entity_name === 'MANUFACTURING_ORDER_LINE'), visibleColumnsRM),
            cfRMLine: CustomFieldHelpers.getDocumentCf(cfV2DocManufacturingOrder?.data?.document_line_custom_fields?.filter((item) => item.entity_name === 'MANUFACTURING_ORDER_LINE'), bom?.bom_lines?.[0]?.bom_line_custom_fields || []),
            visibleColumnsFG: CustomFieldHelpers.updateVisibleColumns(cfV2DocManufacturingOrder?.data?.document_line_custom_fields?.filter((item) => item.entity_name === 'MANUFACTURING_ORDER_FINISHED_GOOD'), visibleColumnsFG),
            cfFGLine: CustomFieldHelpers.getDocumentCf(cfV2DocManufacturingOrder?.data?.document_line_custom_fields?.filter((item) => item.entity_name === 'MANUFACTURING_ORDER_FINISHED_GOOD'), bom?.custom_fields || []),
          }), () => {
          });
        }
      }
    }
  }

  handleMultiProductChangeFG = (tenantSku, key, productData, callback) => {
    this.handleProductChangeFG(tenantSku, key, productData, true, callback);
  }

  getMoComponents(lines) {
    const { finishedGoods } = this.state;
    const skuLines = {};

    const tenantSkuIdList = Object.keys(lines);
    for (let i = 0; i < tenantSkuIdList?.length; i++) {
      const tenantSkuId = tenantSkuIdList[i];
      if (lines[tenantSkuId]?.length) {
        const allSkuLines = Helpers.transformArrayToTree([...lines[tenantSkuId]]);
        for (let j = 0; j < allSkuLines?.length; j++) {
          if (skuLines.hasOwnProperty(allSkuLines[j]?.tenant_product_info?.tenant_product_id)) {
            if (finishedGoods?.length !== 1) {
              skuLines[allSkuLines[j]?.tenant_product_info?.tenant_product_id].estimatedUsage += (allSkuLines[j]?.quantityPerUnit * allSkuLines[j]?.fgQuantity);
              skuLines[allSkuLines[j]?.tenant_product_info?.tenant_product_id].wastage_quantity = (Number(skuLines[allSkuLines[j]?.tenant_product_info?.tenant_product_id].wastage_quantity) + (Number(allSkuLines[j]?.wastage_quantity)));
            }
          } else if (finishedGoods?.length !== 1) {
            skuLines[allSkuLines[j]?.tenant_product_info?.tenant_product_id] = {
              ...allSkuLines[j],
              estimatedUsage: (allSkuLines[j]?.quantityPerUnit || 0) * (allSkuLines[j]?.fgQuantity || 0),
            };
          } else {
            skuLines[allSkuLines[j]?.tenant_product_info?.tenant_product_id] = {
              ...allSkuLines[j],
            };
          }
        }
      }
    }
    return Object.values(skuLines);
  }

  bomLinesWithCustomFields(bomLines, isChildBom, parentQuantity, parentKey, fgTenantProductId) {
    const { cfV2DocManufacturingOrder } = this.props;
    const bomLinesFromState = Object.values(JSON.parse(JSON.stringify(this.state?.bomLines)))?.[0];
    return bomLines.map((bom) => {
      if (bom?.parent_bom_id) {
        const rmLineCFs = cfV2DocManufacturingOrder?.data?.document_line_custom_fields?.filter((item) => item.entity_name === 'MANUFACTURING_ORDER_LINE' && item?.is_active);
        const currentParentkey = bomLinesFromState?.find((line) => line?.tenant_product_info?.tenant_product_id === bom?.tenant_product_info?.tenant_product_id && line?.parent_bom_id)?.key;
        const parentQuantityPerUnit = (((bom.bom_line_quantity * bom?.uom_info?.ratio) / bom?.tenant_product_info?.uom_info?.ratio) || 0) / parentQuantity;
        const estimatedUsage = ((((bom.bom_line_quantity * bom?.uom_info?.ratio) / bom?.tenant_product_info?.uom_info?.ratio) || 0) / (bom.bom_quantity || 0)) * bom.bom_quantity;
        const updatedBom = isChildBom ? {
          ...bom,
          lineCustomFields: CustomFieldHelpers.mergeCustomFields(rmLineCFs, bom?.bom_line_custom_fields) || [],
          currentStock: bom?.tenant_product_info?.on_hand_qty,
          key: currentParentkey,
          parentKey,
          productSkuName: bom?.product_info?.product_sku_name,
          product_category_info: bom?.product_info?.product_category_info,
          bomLineId: bom?.bom_line_id,
          tenantProductId: bom?.tenant_product_info?.tenant_product_id,
          tenant_product_info: {
            ...bom?.tenant_product_info,
            internal_sku_code: bom?.product_info?.internal_sku_code,
          },
          costPerUnit: bom?.tenant_product_info?.cost_price,
          quantityPerUnit: (((bom.bom_line_quantity * bom?.uom_info?.ratio) / bom?.tenant_product_info?.uom_info?.ratio) || 0) / bom.bom_quantity,
          parentQuantityPerUnit,
          estimatedUsage,
          wastage_quantity: (((((bom.bom_line_quantity * bom?.uom_info?.ratio) / bom?.tenant_product_info?.uom_info?.ratio) || 0) / (bom.bom_quantity || 0)) * bom.bom_quantity) * (bom?.wastage_percentage / 100),
          uomId: bom?.tenant_product_info?.uom_info?.uom_id,
          uomInfo: bom?.tenant_product_info?.uom_info,
          uom_info: bom?.tenant_product_info?.uom_info,
          fgQuantity: bom?.quantity || bom?.bom_line_quantity,
          bom_id: bom?.bom_id,
          fg_tenant_product_id: fgTenantProductId,
          child_bom_id: bom?.child_bom_id,
          parent_bom_id: bom?.parent_bom_id,
          bom_line_parent_bom_id: bom?.parent_bom_id,
          // Ensure it's always an array
        } : {
          ...bom,
          lineCustomFields: CustomFieldHelpers.mergeCustomFields(rmLineCFs, bom?.bom_line_custom_fields) || [],
        };
        // If "child_bom_lines" exists, recursively update them
        if (Array.isArray(bom.child_bom_lines) && bom?.child_bom_lines?.length > 0) {
          updatedBom.child_bom_lines = this.bomLinesWithCustomFields(updatedBom?.child_bom_lines, true, parentQuantity, parentKey, bom?.fg_tenant_product_id);
        }
        return updatedBom;
      } return bom;
      // Create a shallow copy of the object and add "lineCustomFields"
    });
  }

  getRawMaterials() {
    const { finishedGoods, bomLines } = this.state;

    const rawMaterials = [];
    const updateChildBomLines = (bomLine, bomLinesArray) => {
      if (!bomLine.child_bom_lines || !Array.isArray(bomLine.child_bom_lines)) {
        return bomLine; // No children, return as is
      }

      return {
        ...bomLine,
        child_bom_lines: bomLine.child_bom_lines.map((child) => {
          const updatedChild = bomLinesArray.find((item) => item?.bom_line_id === child?.bom_line_id);
          return updatedChild ? updateChildBomLines(updatedChild, bomLinesArray) : child;
        }),
      };
    };
    const getUpdatedBomLines = (bomLines, tenant_product_id) => {
      const bomLinesArray = Object.values(bomLines).flat(); // Convert object to array
      if (!Array.isArray(bomLines[tenant_product_id])) return [];

      return bomLines[tenant_product_id].map((bomLine) => updateChildBomLines(bomLine, bomLinesArray));
    };

    const finishedGoodsCopy = JSON.parse(JSON.stringify(finishedGoods));

    finishedGoodsCopy.forEach((fg) => {
      if (fg?.tenant_product_info) {
        const { tenant_product_id } = fg?.tenant_product_info;

        delete fg?.lineCustomFields;
        rawMaterials.push(fg);

        if (bomLines[tenant_product_id]) {
          const updatedRawMaterials = getUpdatedBomLines(bomLines, tenant_product_id);
          const filteredRawMaterials = updatedRawMaterials?.filter((line) => !line?.isChildBom);
          rawMaterials.push(...filteredRawMaterials);
        }
      }
    });

    return rawMaterials;
  }

  currencyConversion(fromUOM, toUOM, price, withPrecision = false) {
    let unitPrice = (Number(price) / Number(fromUOM?.ratio)) * Number(toUOM?.ratio);
    if (withPrecision) {
      unitPrice = QUANTITY(unitPrice, toUOM?.precision);
    }
    return unitPrice;
  }

  getRawMaterialsSummary(lines) {
    const { finishedGoods, trackWastage } = this.state;
    if (lines) {
      let result = [];
      const allSkuLines = Object.values(lines).flat();

      const consolidatedSkuLines = allSkuLines.reduce((acc, curr) => {
        const skuId = curr?.tenant_product_info?.product_sku_id;
        const fgTenantProductId = curr?.fg_tenant_product_id;
        const costPerUnit = Number(curr?.costPerUnit) || 0;
        const estimatedUsage = FormHelpers.uomConversion(curr?.uom_info || curr?.product_info?.uom_info, curr?.product_info?.uom_info, curr?.estimatedUsage);
        const wastageQuantity = FormHelpers.uomConversion(curr?.uom_info || curr?.product_info?.uom_info, curr?.product_info?.uom_info, curr?.wastage_quantity);
        const totalCostForCurrent = costPerUnit * (estimatedUsage + (trackWastage ? wastageQuantity : 0));

        if (!skuId || !fgTenantProductId) return acc;

        if (!acc[skuId]) {
          // Create a new entry with fg_product_info as an array
          acc[skuId] = {
            ...curr,
            estimatedUsage,
            wastage_quantity: wastageQuantity,
            totalEstimatedCost: totalCostForCurrent, // Initialize total cost
            fg_product_info: [{
              fg_tenant_product_id: fgTenantProductId,
              ...curr.tenant_product_info,
            }],
          };
        } else {
          // Accumulate estimated usage and wastage quantity
          acc[skuId].estimatedUsage += estimatedUsage;
          acc[skuId].wastage_quantity += wastageQuantity;
          acc[skuId].totalEstimatedCost += totalCostForCurrent; // Add to total cost

          // Add new fg_tenant_product_id info to the array if it's not already present
          if (!acc[skuId].fg_product_info.some((item) => item.fg_tenant_product_id === fgTenantProductId)) {
            acc[skuId].fg_product_info.push({
              fg_tenant_product_id: fgTenantProductId,
              ...curr.tenant_product_info,
            });
          }
        }
        return acc;
      }, {});

      // Convert object back to array and calculate costPerUnitAverage
      result = Object.values(consolidatedSkuLines).map((item) => ({
        ...item,
        fg_product_info: item.fg_product_info.map((fg) => {
          const matchedFg = finishedGoods.find((fgItem) => fgItem.tenant_product_info?.tenant_product_id === fg.fg_tenant_product_id);
          return matchedFg ? { ...fg, ...matchedFg.tenant_product_info } : fg;
        }),
      }));
      return result;
    }
  }

  handleUomConversion(lines, fgTenantProductId, bomLineId) {
    const { bomLines } = this.state;
    const bomLinesArray = Object.values(bomLines)[0];
    lines = lines.map((line) => {
      if (line?.child_bom_lines?.length > 0 && line?.bom_line_id === bomLineId) {
        bomLinesArray = bomLinesArray?.map((bomLine) => {
          line.child_bom_lines = line.child_bom_lines.map((childBomLine) => {
            if (bomLine?.bom_line_id === childBomLine?.bom_line_id) {
              const childEstimatedUsageInProductUom = FormHelpers.uomConversion(childBomLine?.uom_info || childBomLine?.product_info?.uom_info, childBomLine?.product_info?.uom_info, bomLine?.estimatedUsage);
              const childWastageQuantityinProductUom = FormHelpers.uomConversion(childBomLine?.uom_info || childBomLine?.product_info?.uom_info, childBomLine?.product_info?.uom_info, bomLine?.wastage_quantity);
              const parentEstimatedUsageInProductUom = FormHelpers.uomConversion(line?.uom_info || line?.product_info?.uom_info, line?.product_info?.uom_info, line?.estimatedUsage);
              const parentWastageQuantityinProductUom = FormHelpers.uomConversion(line?.uom_info || line?.product_info?.uom_info, line?.product_info?.uom_info, line?.wastage_quantity);
              const childEstimatedUsagePerParent = (childEstimatedUsageInProductUom / line?.estimatedUsage);
              const childWastageQuantityPerParent = line?.wastage_quantity === 0 ? 0 : (childWastageQuantityinProductUom / line?.wastage_quantity);
              const newEstimatedUsageForChild = FormHelpers.uomConversion(childBomLine?.product_info?.uom_info, childBomLine?.uom_info || childBomLine?.product_info?.uom_info, childEstimatedUsagePerParent * parentEstimatedUsageInProductUom);
              const newWastageQuantityForChild = FormHelpers.uomConversion(childBomLine?.product_info?.uom_info, childBomLine?.uom_info || childBomLine?.product_info?.uom_info, childWastageQuantityPerParent * parentWastageQuantityinProductUom);
              bomLine.estimatedUsage = newEstimatedUsageForChild;
              bomLine.wastageQuantity = newWastageQuantityForChild;
              if (childBomLine?.child_bom_lines?.length > 0) {
                this.handleUomConversion(bomLine?.child_bom_lines, fgTenantProductId, bomLine?.bom_line_id);
              }
            }
          });
          return { ...bomLine };
        });
      }
      return { ...line };
    });
  }

  getMoCharges(lines) {
    const { finishedGoods } = this.state;
    const skuLines = {};
    const tenantSkuIdList = Object.keys(lines);
    for (let i = 0; i < tenantSkuIdList?.length; i++) {
      const tenantSkuId = tenantSkuIdList[i];
      if (lines[tenantSkuId]?.length) {
        const allSkuLines = [...lines[tenantSkuId]];
        for (let j = 0; j < allSkuLines?.length; j++) {
          if (skuLines.hasOwnProperty(allSkuLines[j]?.charge_name)) {
            if (finishedGoods?.length !== 1) {
              skuLines[allSkuLines[j]?.charge_name].charge_amount += (allSkuLines[j]?.costPerUnit * allSkuLines[j]?.fgQuantity);
            }
          } else if (finishedGoods?.length !== 1) {
            skuLines[allSkuLines[j]?.charge_name] = {
              ...allSkuLines[j],
              charge_amount: allSkuLines[j]?.costPerUnit * allSkuLines[j]?.fgQuantity,
            };
          } else {
            skuLines[allSkuLines[j]?.charge_name] = {
              ...allSkuLines[j],
            };
          }
        }
      }
    }
    return Object.values(skuLines);
  }

  getMoProductionRoutes(lines) {
    const { finishedGoods } = this.state;
    const fgPrlMapping = lines;
    const finalFgPrlList = [];
    if (fgPrlMapping) {
      for (let i = 0; i < Object.keys(fgPrlMapping)?.length; i++) {
        const fgTenantProductId = Object.keys(fgPrlMapping)[i];
        const fgPrlProductList = fgPrlMapping[fgTenantProductId]?.br_lines;
        if (fgPrlProductList?.length > 0) {
          finalFgPrlList.push({
            key: uuidv4(),
            bom_route_id: fgPrlMapping[fgTenantProductId]?.bom_route_id,
            is_sequential: fgPrlMapping[fgTenantProductId]?.is_sequential,
            is_time_tracked: fgPrlMapping[fgTenantProductId]?.is_time_tracked,
            fg_product: {
              ...finishedGoods?.find((item) => item?.tenant_product_info?.tenant_product_id == fgTenantProductId)?.bom_info?.product_info || finishedGoods?.find((item) => item?.tenant_product_info?.tenant_product_id == fgTenantProductId)?.tenant_product_info,
              tenant_product_id: fgTenantProductId,
            },
          });
        }
        for (let j = 0; j < fgPrlProductList?.length; j++) {
          finalFgPrlList.push({
            ...fgPrlProductList[j],
            fg_tenant_product_id: fgTenantProductId,
          });
        }
      }
    }
    return finalFgPrlList;
  }

  createMO(withApproval) {
    this.setState({ withApproval });
    const {
      createMO, history, match, updateMO, selectedMO, getMOByIdSuccess, user, location,
    } = this.props;
    const {
      bomLines, bomByProducts, extraCharges, fileList, bomInfo, scheduledDate, remark, tenantDepartmentId, cfManufacturingOrderDoc,
      sellerName, sellerGst, vendorAddress, tenantSellerId, finishedGoods, trackWastage, productionRoutes, selectedTenant, batchLevelJobWorks, restrictMoUser, moUsers, isIssueSfgEnabled,
      fgTenantDepartmentId, rmTenantDepartmentId, bpTenantDepartmentId, deliveryDate, updateDocumentReason, moNumber, docSeqId, initialMoNumber,
    } = this.state;

    this.setState({ formSubmitted: true });
    if (tenantDepartmentId && scheduledDate && deliveryDate && (restrictMoUser ? moUsers?.length > 0 : true) && this.isDataValidFG() && this.isDataValidRM() && (finishedGoods?.length === 1 ? (this.isDataValidRM() && this.isDataValidBP() && this.isDataValidOC()) : true)
      && (sellerGst ? sellerGst?.length === 15 : true)
      && CustomFieldHelpers.isCfValid(cfManufacturingOrderDoc) && this.isDataErrorFree()) {
      if (match?.params?.moId) {
        const temoMoFinishedGoods = finishedGoods.map((item) => {
          const tpId = item?.tenant_product_id || item?.tenant_product_info?.tenant_product_id;
          return {
            mo_fg_id: item?.mo_fg_id,
            mo_id: item?.mo_id,
            bom_id: item?.bom_id,
            quantity: item?.quantity,
            fg_tenant_product_id: item?.fg_tenant_product_id,
            tenant_product_id: item?.tenant_product_id || item?.tenant_product_info?.tenant_product_id,
            product_sku_id: item?.tenant_product_info?.product_sku_id,
            uom_id: item?.uom_info?.uom_id || item?.tenant_product_info?.uom_info?.uom_id,
            uom_info: item?.uom_info || item?.tenant_product_info?.uom_info,
            charges_per_unit: this.getFgCosting(tpId, Number(item?.quantity)).chargePerUnit,
            bom_cost_per_unit: this.getFgCosting(tpId, Number(item?.quantity)).totalCostPerUnit,
            rm_cost_per_unit: this.getFgCosting(tpId, Number(item?.quantity)).rmCostPerUnit,
            custom_fields: CustomFieldHelpers.postCfStructure(item?.lineCustomFields),
          };
        });
        const fgTpIds = finishedGoods?.map((innerItem2) => innerItem2?.tenant_product_id || innerItem2?.tenant_product_info?.tenant_product_id);
        const tempMoLines = Object.keys(bomLines).map((item) => bomLines[item]
          ?.filter((innerItem) => fgTpIds?.includes(innerItem?.fg_tenant_product_id))
          ?.map((innerItem) => ({
            mo_line_id: innerItem?.mo_line_id,
            mo_id: innerItem?.mo_id,
            bom_line_id: innerItem?.isAdhocLine ? null : innerItem?.bom_line_id,
            tenant_product_id: innerItem?.tenant_product_id || innerItem?.tenant_product_info?.tenant_product_id,
            wastage_quantity: Number(innerItem?.wastage_quantity || 0),
            quantity: innerItem?.estimatedUsage,
            bom_id: innerItem?.bom_id,
            uom_id: innerItem?.uom_id || innerItem?.tenant_product_info?.uom_info?.uom_id,
            uom_info: innerItem?.tenant_product_info?.uom_info,
            fg_tenant_product_id: innerItem?.fg_tenant_product_id,
            product_sku_id: innerItem?.product_sku_id,
            child_bom_id: innerItem?.child_bom_id,
            parent_bom_id: innerItem?.parent_bom_id,
            parent_quantity_per_unit: innerItem?.parentQuantityPerUnit,
            custom_fields: CustomFieldHelpers.postCfStructure(innerItem?.lineCustomFields),
          })))?.flat(1).filter((innerItem) => innerItem?.tenant_product_id);
        const tempByProductLines = Object.keys(bomByProducts).map((item) => bomByProducts[item]?.map((innerItem) => ({
          by_product_id: innerItem?.by_product_id,
          mo_id: innerItem?.mo_id,
          tenant_product_id: innerItem?.tenant_product_id || innerItem?.tenant_product_info?.tenant_product_id,
          product_sku_id: innerItem?.product_sku_id,
          quantity: innerItem?.estimatedUsage,
          bom_by_product_id: innerItem?.bom_by_product_id,
          bom_id: innerItem?.bom_id,
          uom_id: innerItem?.uom_id || innerItem?.tenant_product_info?.uom_info?.uom_id,
          uom_info: innerItem?.uom_info || innerItem?.tenant_product_info?.uom_info,
          fg_tenant_product_id: innerItem?.fg_tenant_product_id,
        })))?.flat(1).filter((innerItem) => innerItem?.tenant_product_id);
        let tempExtraCharges;
        if (finishedGoods?.length !== 1) {
          tempExtraCharges = Object.keys(extraCharges).map((item) => extraCharges[item]
            ?.map((innerItem) => ({
              charge_name: innerItem.charge_name,
              est_charge_amount: innerItem?.charge_amount,
              bom_id: innerItem?.bom_id,
              fg_tenant_product_id: innerItem?.fg_tenant_product_id,
            })))?.flat(1).filter((innerItem) => innerItem?.charge_name);
        } else {
          tempExtraCharges = Object.keys(extraCharges).map((item) => extraCharges[item]
            ?.map((innerItem) => ({
              charge_name: innerItem.charge_name,
              est_charge_amount: innerItem?.charge_amount,
              bom_id: innerItem?.bom_id,
              fg_tenant_product_id: innerItem?.fg_tenant_product_id,
            })))?.flat(1).filter((innerItem) => innerItem?.charge_name);
        }
        // Production Route Lines
        const tempProductionRoutesLines = Object.keys(productionRoutes).map((item) => ({
          bom_route_id: productionRoutes[item]?.bom_route_id,
          pr_lines: productionRoutes[item]?.br_lines?.map((i) => ({
            ...i, org_id: user?.tenant_info?.org_id, tenant_id: selectedMO?.tenant_id, attachments: i.attachments ? i.attachments : [],
          })),
          is_sequential: productionRoutes[item]?.is_sequential,
          is_time_tracked: productionRoutes[item]?.is_time_tracked,
          fg_tenant_product_id: productionRoutes[item]?.fg_tenant_product_id,
          production_route_id: productionRoutes[item]?.production_route_id,
        }));

        const payload = {
          mo_id: selectedMO?.mo_id,
          mo_number: selectedMO?.mo_number,
          scheduled_date: scheduledDate,
          delivery_date: deliveryDate,
          enable_wastage_tracking: tenantSellerId ? false : trackWastage,
          job_works_type: batchLevelJobWorks ? 'batch_level' : 'fg_level',
          attachments: fileList?.map((attachment) => ({
            url: attachment?.response?.response?.location || attachment?.url, type: attachment.type, name: attachment.name, uid: attachment.uid,
          })) || [],
          is_restricted: restrictMoUser,
          mo_users: restrictMoUser ? moUsers : null,
          is_issue_sfg_enabled: isIssueSfgEnabled,
          tenant_id: selectedMO?.tenant_id,
          status: withApproval ? 'CONFIRMED' : 'DRAFT',
          tenant_department_id: tenantDepartmentId,
          fg_tenant_department_id: fgTenantDepartmentId,
          rm_tenant_department_id: rmTenantDepartmentId,
          bp_tenant_department_id: bpTenantDepartmentId,
          tenant_seller_id: tenantSellerId || null,
          seller_name: sellerName || null,
          seller_address: vendorAddress || null,
          seller_gst: sellerGst || null,
          remarks: remark,
          mo_finished_goods: temoMoFinishedGoods,
          mo_lines: tempMoLines,
          extra_charges: tempExtraCharges,
          by_product_lines: tempByProductLines,
          production_route: tempProductionRoutesLines.filter((item) => item?.pr_lines),
          tenant_product_id: bomInfo?.tenant_product_info?.tenant_product_id,
          custom_fields: CustomFieldHelpers.postCfStructure(cfManufacturingOrderDoc),
          update_document_reason: updateDocumentReason,
          mo_number: moNumber || '',
        };

        updateMO(payload, (mo) => {
          this.setState({ actionType: '' });
          getMOByIdSuccess(null);
          history.push(`/production/manufacturing-orders/view/${mo?.mo_id}`);
        });
      } else {
        const temoMoFinishedGoods = finishedGoods.map((item) => ({
          bom_id: item?.bom_info?.bom_id,
          quantity: item?.quantity,
          fg_tenant_product_id: item?.bom_info?.tenant_product_info?.tenant_product_id,
          tenant_product_id: item?.bom_info?.tenant_product_info?.tenant_product_id || item?.tenant_product_id,
          product_sku_id: item?.tenant_product_info?.product_sku_id,
          uom_id: item?.bom_info?.tenant_product_info?.uom_info?.uom_id || item?.uom_info?.uom_id || item?.bom_info?.product_info?.uom_info?.uom_id,
          uom_info: item?.bom_info?.tenant_product_info?.uom_info || item?.uom_info || item?.bom_info?.product_info?.uom_info,
          charges_per_unit: this.getFgCosting(item?.tenant_product_info?.tenant_product_id, Number(item?.quantity)).chargePerUnit,
          bom_cost_per_unit: this.getFgCosting(item?.tenant_product_info?.tenant_product_id, Number(item?.quantity)).totalCostPerUnit,
          rm_cost_per_unit: this.getFgCosting(item?.tenant_product_info?.tenant_product_id, Number(item?.quantity)).rmCostPerUnit,
          custom_fields: CustomFieldHelpers.postCfStructure(item?.lineCustomFields),
        }));
        const tempMoLines = Object.keys(bomLines)?.map((item) => bomLines[item]?.map((innerItem) => ({
          tenant_product_id: innerItem?.tenantProductId || innerItem?.tenant_product_id,
          wastage_quantity: Number(innerItem?.wastage_quantity || 0),
          quantity: innerItem?.estimatedUsage,
          bom_line_id: innerItem?.isAdhocLine ? null : innerItem?.bomLineId,
          bom_id: innerItem?.bom_id,
          uom_id: innerItem?.uomId,
          uom_info: innerItem?.uomInfo,
          product_sku_id: innerItem?.product_sku_id,
          fg_tenant_product_id: innerItem?.fg_tenant_product_id,
          child_bom_id: location?.state?.cloneMo ? innerItem?.bom_line_child_bom_id : innerItem?.child_bom_id,
          parent_bom_id: location?.state?.cloneMo ? innerItem?.bom_line_parent_bom_id : innerItem?.parent_bom_id,
          parent_quantity_per_unit: innerItem?.parentQuantityPerUnit,
          custom_fields: CustomFieldHelpers.postCfStructure(innerItem?.lineCustomFields),
        })))?.flat(1).filter((innerItem) => innerItem?.tenant_product_id);
        const tempByProductLines = Object.keys(bomByProducts).map((item) => bomByProducts[item]?.map((innerItem) => ({
          tenant_product_id: innerItem?.tenantProductId,
          product_sku_id: innerItem?.product_sku_id,
          quantity: innerItem?.estimatedUsage,
          bom_by_product_id: innerItem?.bomByProductId,
          bom_id: innerItem?.bom_id,
          uom_id: innerItem?.uomId || innerItem?.uom_id,
          uom_info: innerItem?.uomInfo,
          fg_tenant_product_id: innerItem?.fg_tenant_product_id,
        })))?.flat(1).filter((innerItem) => innerItem?.tenant_product_id);

        let tempExtraCharges;
        if (finishedGoods?.length !== 1) {
          tempExtraCharges = Object.keys(extraCharges).map((item) => extraCharges[item]
            ?.map((innerItem) => ({
              charge_name: innerItem.charge_name,
              est_charge_amount: innerItem?.charge_amount,
              bom_id: innerItem?.bom_id,
              fg_tenant_product_id: innerItem?.fg_tenant_product_id,
            })))?.flat(1).filter((innerItem) => innerItem?.charge_name);
        } else {
          tempExtraCharges = Object.keys(extraCharges).map((item) => extraCharges[item]
            ?.map((innerItem) => ({
              charge_name: innerItem.charge_name,
              est_charge_amount: innerItem?.charge_amount,
              bom_id: innerItem?.bom_id,
              fg_tenant_product_id: innerItem?.fg_tenant_product_id,
            })))?.flat(1).filter((innerItem) => innerItem?.charge_name);
        }
        // Production Route Lines
        const tempProductionRoutesLines = Object.keys(productionRoutes).map((item) => ({
          bom_route_id: productionRoutes[item]?.bom_route_id,
          pr_lines: productionRoutes[item]?.br_lines?.map((i) => ({
            ...i,
            org_id: user?.tenant_info?.org_id,
            route_line_charges: [
              {
                charge_name: 'Fixed Charges',
                charge_amount: i.fixed_charge,
                charge_type: 'fixed',
              },
              {
                charge_name: 'Per Unit Charge',
                charge_amount: i.unit_charge,
                charge_type: 'unit',
              },
            ],
          })),
          is_sequential: productionRoutes[item]?.is_sequential,
          is_time_tracked: productionRoutes[item]?.is_time_tracked,
          fg_tenant_product_id: productionRoutes[item]?.fg_tenant_product_id,
        }));

        const payload = {
          scheduled_date: scheduledDate,
          delivery_date: deliveryDate,
          attachments: fileList?.map((attachment) => ({
            url: attachment?.response?.response?.location || attachment?.url, type: attachment.type, name: attachment.name, uid: attachment.uid,
          })) || [],
          tenant_id: selectedTenant,
          is_restricted: restrictMoUser,
          mo_users: restrictMoUser ? moUsers : null,
          is_issue_sfg_enabled: isIssueSfgEnabled,
          status: withApproval ? 'CONFIRMED' : 'DRAFT',
          tenant_department_id: tenantDepartmentId,
          fg_tenant_department_id: fgTenantDepartmentId,
          rm_tenant_department_id: rmTenantDepartmentId,
          bp_tenant_department_id: bpTenantDepartmentId,
          tenant_seller_id: tenantSellerId || null,
          seller_name: sellerName || null,
          seller_address: vendorAddress || null,
          seller_gst: sellerGst || null,
          remarks: remark,
          mo_finished_goods: temoMoFinishedGoods,
          mo_lines: tempMoLines,
          extra_charges: tempExtraCharges,
          by_product_lines: tempByProductLines,
          production_route: tempProductionRoutesLines?.filter((item) => item?.pr_lines),
          tenant_product_id: bomInfo?.tenant_product_info?.tenant_product_id,
          enable_wastage_tracking: trackWastage,
          job_works_type: batchLevelJobWorks ? 'batch_level' : 'fg_level',
          custom_fields: CustomFieldHelpers.postCfStructure(cfManufacturingOrderDoc),
          mo_number: initialMoNumber?.toLowerCase()?.trim() === moNumber?.toLowerCase()?.trim() ? null : moNumber,
          seq_id: docSeqId || null,
        };
        createMO(payload, (mo) => {
          this.setState({ actionType: '' });
          history.push(`/production/manufacturing-orders/view/${mo?.mo_id}`);
        });
      }
    } else {
      notification.open({
        type: 'error',
        message: 'Please provide all mandatory inputs',
        duration: 4,
        placement: 'top',
      });
    }
  }

  customInputChange(fieldValue, cfId) {
    const { cfManufacturingOrderDoc } = this.state;
    const newCustomField = cfManufacturingOrderDoc?.map((customField) => {
      if (customField?.cfId === cfId) {
        if (customField?.fieldType === 'ATTACHMENT') {
          return {
            ...customField,
            fieldValue: fieldValue?.map((attachment) => ({
              url: attachment?.response?.response?.location || attachment?.url, type: attachment.type, name: attachment.name, uid: attachment.uid,
            })),
          };
        }
        return {
          ...customField,
          fieldValue: customField?.fieldType === 'NUMBERS' ? Number(fieldValue) : fieldValue,
        };
      }
      return {
        ...customField,
      };
    });
    this.setState({
      cfManufacturingOrderDoc: newCustomField,
    });
  }

  rmCustomLineInputChange(fieldValue, key, fgTenantProductId, wastageQuantity) {
    const { bomLines } = this.state;
    const bomLinesCopy = JSON.parse(JSON.stringify(bomLines));
    const productBomLines = bomLinesCopy[fgTenantProductId];
    const qtyCf = fieldValue?.find((j) => j?.fieldName === 'Quantity');
    let record;
    for (let i = 0; i < productBomLines?.length; i++) {
      if (productBomLines[i]?.key === key) {
        productBomLines[i].lineCustomFields = fieldValue;
        productBomLines[i].estimatedUsage = parseFloat(Number(qtyCf?.fieldValue));
        if (Number(productBomLines[i].wastage_percentage) > 0) {
          productBomLines[i].wastage_quantity = parseFloat(Number(Number(qtyCf?.fieldValue) * (Number(productBomLines[i].wastage_percentage) / 100)));
        }
        record = productBomLines[i];
      }
    }
    bomLinesCopy[fgTenantProductId] = productBomLines;
    this.setState({
      bomLines: bomLinesCopy,
    }, () => {
      this.handleRmQuantityChange(record?.fg_tenant_product_id, qtyCf?.fieldValue, key, record?.wastage_quantity);
    });
  }

  fgCustomLineInputChange(fieldValue, key) {
    const { finishedGoods } = this.state;
    const copyData = JSON.parse(JSON.stringify(finishedGoods));
    let record;
    const qtyCf = fieldValue?.find((j) => j?.fieldName === 'Quantity');
    for (let i = 0; i < copyData.length; i++) {
      if (copyData[i]?.key === key) {
        record = copyData[i];
        copyData[i].lineCustomFields = fieldValue;
        copyData[i].quantity = qtyCf?.fieldValue;
      }
    }
    this.setState({ finishedGoods: copyData }, () => {
      this.handleFgQuantityChange(record?.tenant_product_info?.tenant_product_id, parseFloat(qtyCf?.fieldValue), record?.key);
    });
  }

  handleDeliveryDateChange = (value) => {
    const { scheduledDate } = this.state;

    // Perform validation: Delivery date cannot be earlier than scheduled date
    if (scheduledDate && value && value.isBefore(scheduledDate, 'day')) {
      // add notification here
      notification.open({
        type: 'error',
        message: 'Delivery date cannot be earlier than scheduled date',
        duration: 4,
        placement: 'top',
      });
      return;
    }

    this.setState({ deliveryDate: value });
  };

  disabledDate = (current) => {
    const { scheduledDate } = this.state;

    // Disable dates before the scheduled date
    return current && scheduledDate ? current.isBefore(scheduledDate, 'day') : false;
  };

  getReadyToProduce = (fgTpId) => {
    const { finishedGoods, bomLines } = this.state;

    const rawMaterials = bomLines[fgTpId];

    const rawMaterialReadyToProduceArray = rawMaterials?.map((m) => Number(m.currentStock || 0) / (Number(m.quantityPerUnit || 0) * (1 + Number(m.wastage_percentage || 0) / 100))) || [];

    const containsZeroOrNegative = rawMaterialReadyToProduceArray?.some((value) => value <= 0);
    return QUANTITY(
      rawMaterialReadyToProduceArray.length ? Math.min(...containsZeroOrNegative ? [...rawMaterialReadyToProduceArray, 0] : rawMaterialReadyToProduceArray) || 0 : 0, finishedGoods[0]?.tenant_product_info?.uom_info?.precision,
    );
  };

  groupByFgTenantProductId = (bomLineArr) => {
    const result = {};

    for (let i = 0; i < bomLineArr.length; i++) {
      const item = bomLineArr[i];
      const { fg_tenant_product_id } = item;

      if (!result[fg_tenant_product_id]) {
        result[fg_tenant_product_id] = [];
      }

      result[fg_tenant_product_id].push(item);
    }

    this.setState({ bomLines: result });
  };

  render() {
    const {
      createMOLoading, getMOByIdLoading, updateMOLoading, user, getBOM, match, getBOMByIdLoading,
      selectedMO, getJobWorkByMoIdLoading, priceMasking,
    } = this.props;
    const {
      formSubmitted, bomLines, vendorAddress, showAddressDrawer, sellerId, sellerGst, productionRoutes,
      scheduledDate, actionType, tenantSellerId, finishedGoods, currentTab, restrictMoUser,
      fileList, bomByProducts, extraCharges, remark, tenantDepartmentId, rmTenantDepartmentId, fgTenantDepartmentId, bpTenantDepartmentId, cfManufacturingOrderDoc, isFlexible, isIssueSfgEnabled, cfFGLine, cfRMLine,
      trackWastage, selectedTenant, deliveryDate, moUsers, visibleColumnsFG, visibleColumnsRM, updateDocumentReason, costingMethod, docSeqId, moNumber,
    } = this.state;
    const accessRestriction = user?.tenant_info?.production_config?.addons?.access_restriction?.is_active;
    const multiLevelBomEnabled = user?.tenant_info?.production_config?.addons?.multilevel_bill_of_material?.is_active;
    const accountingGSTTransactionAsPerMaster = user?.tenant_info?.purchase_config?.sub_modules?.vendor?.settings?.gst_details_in_transaction === 'AS_PER_MASTER';
    const { isDataMaskingPolicyEnable, isHideCostPrice, isHideSellingPrice } = priceMasking;

    let copyDocLevelError;
    let copyLineLevelError;
    if (formSubmitted) {
      const { docLevelError, lineLevelError } = this.getManufacturingOrderErrors();
      copyDocLevelError = docLevelError;
      copyLineLevelError = lineLevelError;
    }

    const pricingOptions = [
      { label: 'Normal', value: 'NORMAL' },
      { label: 'Past 3 months Weighted Average', value: '3_MONTH_WEIGHTED_AVERAGE' },
      { label: 'Past 6 months Weighted Average', value: '6_MONTH_WEIGHTED_AVERAGE' },
    ];

    return (
      <Fragment>
        {(getMOByIdLoading && match.params.moId) ? (
          <div className="mo-form__loading__wrapper">
            {[1, 2, 3, 4, 5, 6, 7, 8].map((item) => (
              <div key={item} className="mo-form__loading__wrapper-row">
                <div className="mo-form__loading__wrapper-row__label loadingBlock" />
                <div className="mo-form__loading__wrapper-row__value loadingBlock" />
              </div>
            ))}
            <div className="mo-form__loading__wrapper-table loadingBlock" />
            <div className="mo-form__loading__wrapper-details loadingBlock" />
          </div>
        )
          : (
            <div className="mo-form__wrapper form__wrapper">
              {(formSubmitted && (copyDocLevelError?.length > 0 || copyLineLevelError?.length > 0)) && (
                <ErrorHandle message="Mandatory fields required" docLevelErrors={copyDocLevelError} lineLevelErrors={copyLineLevelError} />
              )}
              <div className="ant-row">
                <div className="ant-col-md-24">
                  <div className="form__section">
                    <div className="flex-display flex-align-c mg-bottom-5 pd-right-15">
                      <H3Text text="PART A" className="form__section-title" />
                      <div className="form__section-line" />
                      <div style={{ display: 'flex', justifyContent: 'end', alignItems: 'center' }}>
                        <CustomDocumentInputs
                          customFields={cfManufacturingOrderDoc}
                          updateCustomFields={(cf) => this.setState({ cfManufacturingOrderDoc: cf })}
                        />
                      </div>
                    </div>
                  </div>
                  <div className="form__section-inputs mg-bottom-20">
                    <div className="ant-row">
                      <DocumentNumberSeqInput
                        valueFromProps={moNumber}
                        updateCase={this.props?.match?.params?.moId}
                        setInitialDocSeqNumber={(value) => this.setState({ initialMoNumber: value })}
                        entityName="MANUFACTURING_ORDER"
                        docSeqId={docSeqId}
                        tenantId={selectedTenant}
                        onChangeFromProps={(event, newValue, seqId) => {
                          this.setState({
                            moNumber: newValue ? (newValue || '') : (event?.target?.value || ''),
                            docSeqId: seqId,
                          })
                        }}
                        docTitle="Manufacturing Order #"
                        formSubmitted={formSubmitted}
                      />
                      <div className="ant-col-md-6">
                        <TenantSelector
                          selectedTenant={selectedTenant}
                          showSearch
                          onChange={(value) => {
                            this.setState({
                              selectedTenant: value,
                              tenantDepartmentId: user?.user_tenants?.find((item) => item?.tenant_id === value)?.default_department_for_production,
                              rmTenantDepartmentId: user?.user_tenants?.find((item) => item?.tenant_id === value)?.default_department_for_rm,
                              fgTenantDepartmentId: user?.user_tenants?.find((item) => item?.tenant_id === value)?.default_department_for_fg,
                              bpTenantDepartmentId: user?.user_tenants?.find((item) => item?.tenant_id === value)?.default_department_for_bp,
                              sellerId: '',
                              sellerName: '',
                              sellerGst: '',
                              tenantSellerId: '',
                              vendorAddress: '',
                              finishedGoods: [{
                                key: uuidv4(), asset1: '', product_name: '', quantity: '', unitPrice: '', lot: 0, taxId: '', discount: 0,
                              }],
                              bomByProducts: [],
                              extraCharges: [],
                              bomLines: [],
                              productionRoutes: [],
                              currentTab: '/finished-goods',
                            });
                          }}
                          showAll={false}
                          title="Location"
                          customStyle={{ height: '28px', border: 'none' }}
                          labelClassName="form__input-row__label"
                          inputClassName="form__input-row__input"
                          containerClassName="form__input-row"
                          noDropdownAlign
                          placeholder="Select Business Unit"
                          includedTenants={Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.MO, Helpers.permissionTypes.CREATE)}
                          disabled={selectedMO}
                        />
                      </div>
                      <div className="ant-col-md-6">
                        <div className="form__input-row">
                          <H3Text text="Department" className="form__input-row__label" />
                          <div className="form__input-row__input">
                            <SelectDepartment
                              hideTitle
                              tenantId={selectedTenant}
                              selectedDepartment={tenantDepartmentId}
                              noDropdownAlign
                              onChange={(value) => {
                                this.setState({
                                  tenantDepartmentId: value?.tenant_department_id,
                                  finishedGoods: [{
                                    key: uuidv4(), asset1: '', product_name: '', quantity: '', unitPrice: '', lot: 0, taxId: '', discount: 0,
                                  }],
                                  bomByProducts: [],
                                  extraCharges: [],
                                  bomLines: [],
                                  productionRoutes: [],
                                  currentTab: '/finished-goods',
                                });
                              }}
                              tenentLevelDepartment
                              emptyNotAllowed
                              loading={createMOLoading || updateMOLoading}
                              disabled={createMOLoading || updateMOLoading || match?.params?.moId}
                            />
                          </div>
                        </div>
                      </div>
                      <div className="ant-col-md-6">
                        <div className="form__input-row">
                          <H3Text text="Finished Goods Department" className="form__input-row__label" />
                          <div className="form__input-row__input">
                            <SelectDepartment
                              hideTitle
                              tenantId={selectedTenant}
                              selectedDepartment={fgTenantDepartmentId}
                              noDropdownAlign
                              onChange={(value) => {
                                this.setState({
                                  fgTenantDepartmentId: value?.tenant_department_id,
                                  finishedGoods: [{
                                    key: uuidv4(), asset1: '', product_name: '', quantity: '', unitPrice: '', lot: 0, taxId: '', discount: 0,
                                  }],
                                  bomByProducts: [],
                                  extraCharges: [],
                                  bomLines: [],
                                  productionRoutes: [],
                                  currentTab: '/finished-goods',
                                });
                              }}
                              tenentLevelDepartment
                              emptyNotAllowed
                              loading={createMOLoading || updateMOLoading}
                              disabled={createMOLoading || updateMOLoading || match?.params?.moId}
                              inputClassName="orgFormInput input"
                            />
                          </div>
                        </div>
                      </div>
                      <div className="ant-col-md-6">
                        <div className="form__input-row">
                          <H3Text text="Raw Material Department" className="form__input-row__label" />
                          <div className="form__input-row__input">
                            <SelectDepartment
                              hideTitle
                              tenantId={selectedTenant}
                              selectedDepartment={rmTenantDepartmentId}
                              noDropdownAlign
                              onChange={(value) => {
                                this.setState({
                                  rmTenantDepartmentId: value?.tenant_department_id,
                                  finishedGoods: [{
                                    key: uuidv4(), asset1: '', product_name: '', quantity: '', unitPrice: '', lot: 0, taxId: '', discount: 0,
                                  }],
                                  bomByProducts: [],
                                  extraCharges: [],
                                  bomLines: [],
                                  productionRoutes: [],
                                  currentTab: '/finished-goods',
                                });
                                getBOM(user?.tenant_info?.org_id, '', '', '', '', '', '', '', value?.tenant_department_id, 'list');
                              }}
                              tenentLevelDepartment
                              emptyNotAllowed
                              loading={createMOLoading || updateMOLoading}
                              disabled={createMOLoading || updateMOLoading || match?.params?.moId}
                            />
                          </div>
                        </div>
                      </div>
                      <div className="ant-col-md-6">
                        <div className="form__input-row">
                          <H3Text text="By Products Department" className="form__input-row__label" />
                          <div className="form__input-row__input">
                            <SelectDepartment
                              hideTitle
                              tenantId={selectedTenant}
                              selectedDepartment={bpTenantDepartmentId}
                              noDropdownAlign
                              onChange={(value) => {
                                this.setState({
                                  bpTenantDepartmentId: value?.tenant_department_id,
                                  finishedGoods: [{
                                    key: uuidv4(), asset1: '', product_name: '', quantity: '', unitPrice: '', lot: 0, taxId: '', discount: 0,
                                  }],
                                  bomByProducts: [],
                                  extraCharges: [],
                                  bomLines: [],
                                  productionRoutes: [],
                                  currentTab: '/finished-goods',
                                });
                              }}
                              tenentLevelDepartment
                              emptyNotAllowed
                              loading={createMOLoading || updateMOLoading}
                              disabled={createMOLoading || updateMOLoading || match?.params?.moId}
                            />
                          </div>
                        </div>
                      </div>
                      <div className="ant-col-md-6">
                        <div className="form__input-row">
                          <H3Text text="Scheduled Start Date" className="form__input-row__label" required />
                          <div className={`form__input-row__input ${(formSubmitted && !scheduledDate) ? 'form__input-row__input-error' : ''}`}>
                            <DatePicker
                              value={scheduledDate}
                              onChange={(value) => {
                                this.setState({ scheduledDate: value });
                              }}
                              style={{ width: '100%' }}
                              disabled={createMOLoading || updateMOLoading}
                              loading={createMOLoading || updateMOLoading}
                              format="DD/MM/YYYY"
                            />
                          </div>
                        </div>
                      </div>
                      <div className="ant-col-md-6">
                        <div className="form__input-row">
                          <H3Text text="Completion Date" className="form__input-row__label" required />
                          <div className={`form__input-row__input ${(formSubmitted && !deliveryDate) ? 'form__input-row__input-error' : ''}`}>
                            <DatePicker
                              value={deliveryDate}
                              onChange={(value) => {
                                this.handleDeliveryDateChange(value);
                              }}
                              style={{ width: '100%' }}
                              disabled={createMOLoading || updateMOLoading}
                              loading={createMOLoading || updateMOLoading}
                              disabledDate={this.disabledDate}
                              format="DD/MM/YYYY"
                            />
                          </div>
                        </div>
                      </div>
                      <div className="ant-col-md-6">
                        <SelectSeller
                          onChange={(value) => {
                            this.setState({
                              sellerId: value?.seller_id,
                              sellerName: value?.seller_info?.seller_name,
                              sellerGst: value?.seller_info?.gst_number,
                              tenantSellerId: value?.tenant_seller_id,
                              vendorAddress: value?.seller_info?.office_address_details,
                              trackWastage: false,
                            });
                          }}
                          title={(
                            <div style={{ display: 'flex', alignItems: 'center' }}>
                              <div>Select Sub Contractor</div>
                              {!user?.tenant_info?.purchase_config?.sub_modules?.vendor?.is_active && (
                                <Popconfirm
                                  placement="topRight"
                                  title="This feature is not accessible within your current plan to use this feature contact us."
                                  onConfirm={() => window.Intercom('showNewMessage')}
                                  okText="Contact Us"
                                  cancelText="Cancel"
                                >
                                  <img className="barcode-restrict" style={{ marginLeft: '4px' }} src={cdnUrl('crown2.png', 'images')} alt="premium" />
                                </Popconfirm>
                              )}
                            </div>
                          )}
                          selectedSeller={tenantSellerId}
                          tenantId={selectedTenant}
                          disabled={createMOLoading || updateMOLoading || !user?.tenant_info?.purchase_config?.sub_modules?.vendor?.is_active}
                          containerClass="orgInputContainer form__input-row"
                          inputClassName="form-seller__selector form__input-row__input"
                          labelClassName="form__input-row__label"
                          showAddVendor
                          getPopupContainer
                          notRequired
                          isSubcontractor
                        />
                      </div>
                      {tenantSellerId && (
                        <div className="ant-col-md-6">
                          <div className="form__input-row">
                            <H3Text text="Sub Contractor GST" className="form__input-row__label" />
                            <H3FormInput
                              name="a valid gst number"
                              type="text"
                              containerClassName="orgInputContainer form__input-row__input"
                              labelClassName="orgFormLabel"
                              inputClassName="orgFormInput input"
                              placeholder=""
                              onChange={(e) => {
                                this.setState({ sellerGst: e.target.value });
                              }}
                              value={sellerGst}
                              disabled={createMOLoading || updateMOLoading || accountingGSTTransactionAsPerMaster}
                              loading={createMOLoading || updateMOLoading}
                              showError={formSubmitted && (sellerGst ? sellerGst?.length !== 15 : false)}
                            />
                          </div>
                        </div>
                      )}
                      {currentTab === '/finished-goods' && (
                        <div className="ant-col-md-6">
                          <div className="form__input-row">
                            <H3Text text="Costing Method" className="form__input-row__label" />
                            <div className="form__input-row__input">
                              <PRZSelect
                                value={costingMethod}
                                onChange={(value) => {
                                  this.setState({ costingMethod: value });
                                }}
                                options={pricingOptions}
                              />
                            </div>
                          </div>
                        </div>
                      )}
                      <CustomFieldV3
                        customFields={cfManufacturingOrderDoc}
                        formSubmitted={formSubmitted}
                        customInputChange={(value, cfId) => this.customInputChange(value, cfId)}
                        wrapperClassName="ant-col-md-6"
                        containerClassName="form__input-row"
                        labelClassName="form__input-row__label"
                        inputClassName="form__input-row__input"
                        errorClassName="form__input-row__input-error"
                        hideTitle
                      />
                    </div>
                    <div className="ant-col-md-12" />
                    <div className="ant-row">
                      <div className="ant-col-md-6">
                        <div className="form__input-row">
                          <H3Text text="Internal Remarks" className="form__input-row__label" />
                          <textarea
                            onChange={(event) => {
                              this.setState({ remark: event.target.value });
                            }}
                            value={remark}
                            disabled={createMOLoading || updateMOLoading}
                            style={{
                              resize: 'none',
                              backgroundColor: 'rgb(239, 239, 239)',
                              border: 'none',
                              borderRadius: '4px',
                              marginRight: '15px',
                              height: '103px',
                              padding: '4px 8px',
                              width: 'calc(100% - 15px)',
                            }}
                          />
                        </div>
                      </div>
                      {tenantSellerId && (
                        <div className="ant-col-md-6">
                          <div className="form__input-row">
                            <H3Text text="Sub Contractor Address" className="form__input-row__label" />
                            <div className="form__input-row__address__wrapper">
                              <div className="form__input-row__address">
                                {vendorAddress && (
                                  <div className="form__input-row__address-info">
                                    <div className="form__input-row__address-l1">{vendorAddress?.address1}</div>
                                    <div
                                      className="form__input-row__address-l2"
                                    >
                                      {`${vendorAddress?.city}, ${vendorAddress?.state}, ${vendorAddress?.postal_code}, ${vendorAddress?.country}`}
                                    </div>
                                  </div>
                                )}
                                {!vendorAddress && <H3Text text="Select address.." className="form__input-row__address-placeholder" />}
                                <div className="form__input-row__address-icon" onClick={() => this.setState({ showAddressDrawer: true })}>
                                  <EditFilled />
                                  {' '}
                                  Update
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                      <div className="ant-col-md-6 mg-top-10">
                        <div className="form__input-row">
                          <Checkbox
                            checked={isIssueSfgEnabled}
                            onChange={(e) => this.setState({
                              isIssueSfgEnabled: !isIssueSfgEnabled,
                              restrictionCheck: e.target.checked !== false,
                            })}
                            disabled={createMOLoading || updateMOLoading || !multiLevelBomEnabled}
                          >
                            <span style={{ fontWeight: '500', fontSize: '12px', marginLeft: '5px' }}>Track SFG Production</span>
                            {!multiLevelBomEnabled && (
                              <Popconfirm
                                placement="topRight"
                                title="This feature is not accessible within your current plan to use this feature contact us."
                                onConfirm={() => window.Intercom('showNewMessage')}
                                okText="Contact Us"
                                cancelText="Cancel"
                              >
                                <img className="mo-form-premium-img" src={cdnUrl('crown2.png', 'images')} alt="premium" />
                              </Popconfirm>
                            )}
                          </Checkbox>
                        </div>
                        <div className="form__input-row">
                          <Checkbox checked={trackWastage} onChange={() => this.setState({ trackWastage: !trackWastage })} className="mo-form__input-row__input" disabled={tenantSellerId}>
                            <span style={{ fontWeight: '500', fontSize: '12px', marginLeft: '5px' }}>Track Wastage of Raw Materials</span>
                          </Checkbox>
                        </div>
                        <div className="form__input-row">
                          <Checkbox
                            checked={restrictMoUser}
                            onChange={(e) => this.setState({
                              restrictMoUser: !restrictMoUser,
                              restrictionCheck: e.target.checked !== false,
                            })}
                            disabled={createMOLoading || updateMOLoading || !accessRestriction}
                          >
                            <span style={{ fontWeight: '500', fontSize: '12px', marginLeft: '5px' }}>Restrict Access</span>
                            {!accessRestriction && (
                              <Popconfirm
                                placement="topRight"
                                title="This feature is not accessible within your current plan to use this feature contact us."
                                onConfirm={() => window.Intercom('showNewMessage')}
                                okText="Contact Us"
                                cancelText="Cancel"
                              >
                                <img className="mo-form-premium-img" src={cdnUrl('crown2.png', 'images')} alt="premium" />
                              </Popconfirm>
                            )}
                          </Checkbox>
                        </div>
                        {restrictMoUser && (
                          <div className="form__input-row">
                            <H3Text
                              text="Authorized Users"
                              className="form__input-row__label"
                            />
                            <div className={`form__input-row__input ${(formSubmitted && !moUsers?.length) ? 'form__input-row__input-error' : ''}`}>
                              <SelectAppUser
                                hideTitle
                                labelClassName="orgFormLabel"
                                inputClassName="orgFormInput"
                                selectedUser={moUsers}
                                onChange={(value) => this.setState({ moUsers: value })}
                                disabled={createMOLoading || updateMOLoading}
                                isMultiple
                                placeholder="Select authorised users.."
                                allUsers
                                maxTagCount="responsive"
                              />
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <div style={{
                  display: 'flex', alignItems: 'center', width: '100%', marginTop: '20px',
                }}
                >
                  <div style={{ width: 'calc(100% - 10px)' }}>
                    <Tabs
                      activeKey={currentTab}
                      onChange={(key) => {
                        this.setState({ currentTab: key });
                      }}
                      mode="horizontal"
                      type="card"
                    >
                      <TabPane
                        tab={(
                          <span>
                            Finished Goods &nbsp;
                            <Badge count={finishedGoods?.filter((item) => item?.bom_id)?.length} />
                          </span>
                        )}
                        key="/finished-goods"
                      />
                      <TabPane
                        tab={(
                          <span>
                            Raw Materials &nbsp;
                            <Badge count={this.getMoComponents(bomLines)?.filter((item) => !item?.fg_product)?.length} />
                          </span>
                        )}
                        key="/raw-materials"
                        disabled={!finishedGoods?.filter((item) => item?.bom_id)?.length}
                      />
                      <TabPane
                        tab={(
                          <span>

                            Raw Materials Summary&nbsp;
                            {/* <Badge count={this.getMoComponents(bomLines)?.filter((item) => !item?.fg_product)?.length} /> */}
                          </span>
                        )}
                        key="/raw-materials-summary"
                        disabled={!finishedGoods?.filter((item) => item?.bom_id)?.length}
                      />
                      <TabPane
                        tab={(
                          <span>
                            By Products &nbsp;
                            <Badge count={this.getMoComponents(bomByProducts)?.filter((item) => !item?.fg_product)?.length} />
                          </span>
                        )}
                        key="/by-products"
                        disabled={!finishedGoods?.filter((item) => item?.bom_id)?.length}
                      />
                      <TabPane
                        tab={(
                          <span>
                            Additional Charges &nbsp;
                          </span>
                        )}
                        key="/additional-charges"
                        disabled={!finishedGoods?.filter((item) => item?.bom_id)?.length}
                      />
                      {user?.tenant_info?.production_config?.addons?.factory_setup?.is_active && !user?.tenant_info?.global_config?.settings?.enable_jw_v2 && (
                        <TabPane
                          tab={(
                            <span>
                              Job Cards &nbsp;
                              <Badge count={this.getMoProductionRoutes(productionRoutes)?.filter((item) => !item?.fg_product)?.length} />
                            </span>
                          )}
                          key="/job-works"
                          disabled={!finishedGoods?.filter((item) => item?.bom_id)?.length}
                        />
                      )}
                    </Tabs>
                  </div>
                  {currentTab === '/finished-goods' && (
                    <div>
                      <CustomDocumentColumns
                        visibleColumns={visibleColumnsFG}
                        setVisibleColumns={(data) => this.setState({ visibleColumnsFG: data })}
                        customColumns={cfFGLine}
                        data={finishedGoods}
                        updateData={(updatedData) => this.setState({ finishedGoods: updatedData })}
                      />
                    </div>
                  )}
                  {currentTab === '/raw-materials' && (
                    <div>
                      <CustomDocumentColumns
                        visibleColumns={visibleColumnsRM}
                        setVisibleColumns={(data) => this.setState({ visibleColumnsRM: data })}
                        customColumns={cfRMLine}
                        data={this.getMoComponents(bomLines)}
                        updateData={(updatedData) => this.groupByFgTenantProductId(updatedData)}
                      />
                    </div>
                  )}
                </div>

                {currentTab === '/finished-goods' && (
                  <div className="mo-form__lines-wrapper">
                    <FinishedGood
                      handleDelete={this.handleDeleteFinishedGood}
                      handleProductChange={this.onSelectBom}
                      handleAdhocProductChange={this.handleProductChangeFG}
                      handleAdhocProductChangeValue={this.handleProductChangeValue}
                      handleQuantityChange={this.handleFgQuantityChange}
                      handleChangeRemarks={this.handleChangeRemarks}
                      data={[...finishedGoods.filter((item) => item?.tenant_product_info?.product_sku_id), ...finishedGoods.filter((item) => !item?.tenant_product_info?.product_sku_id)]}
                      addNewRow={() => this.addNewRowFinishedGood()}
                      updateData={(updatedData) => this.setState({ finishedGoods: updatedData })}
                      formSubmitted={formSubmitted}
                      loading={getMOByIdLoading || createMOLoading || updateMOLoading || getBOMByIdLoading}
                      excludedProducts={this.getExcludedProduct()}
                      tenantDepartmentId={tenantDepartmentId}
                      selectedTenant={selectedTenant}
                      includeInActiveBOM
                      getFgCosting={(fgId, qty) => this.getFgCosting(fgId, qty)}
                      getReadyToProduce={(fgTpId) => this.getReadyToProduce(fgTpId)}
                      cfFGLine={cfFGLine}
                      visibleColumns={visibleColumnsFG}
                      customLineInputChange={(value, key) => this.fgCustomLineInputChange(value, key)}
                      handleMultiProductChange={this.handleMultiProductChangeFG}
                    />

                    <div style={{ display: 'flex' }}>
                      <div
                        className={`new-row-button ${finishedGoods?.filter((item) => !item?.tenant_product_info?.tenant_product_id)?.find((item) => !item.isAdhocLine) ? 'new-row-button__disabled' : ''}`}
                        onClick={() => {
                          ((!createMOLoading && !updateMOLoading && !finishedGoods?.filter((item) => !item?.tenant_product_info?.tenant_product_id)?.find((item) => !item.isAdhocLine)) && this.addNewRowFinishedGood());
                        }}
                      >
                        <FontAwesomeIcon icon={faPlusCircle} />
                        <div style={{ marginLeft: '5px' }}>New Item</div>
                      </div>

                      <div
                        className={`new-row-button ${finishedGoods?.filter((item) => !item?.tenant_product_info?.tenant_product_id)?.find((item) => item.isAdhocLine) ? 'new-row-button__disabled' : ''}`}
                        onClick={() => {
                          ((!createMOLoading && !updateMOLoading && !finishedGoods?.filter((item) => !item?.tenant_product_info?.tenant_product_id)?.find((item) => item.isAdhocLine)) && this.addNewRowFinishedGood(true));
                        }}
                        style={{ marginLeft: '10px' }}
                      >
                        <FontAwesomeIcon icon={faPlusCircle} />
                        <div style={{ marginLeft: '5px' }}>ADHOC Item</div>
                      </div>
                    </div>
                  </div>
                )}

                {currentTab === '/raw-materials' && (
                  <div className="mo-form__lines-wrapper">
                    <NewRawMaterial
                      data={this.getRawMaterials()}
                      handleDelete={this.handleDeleteNewRawMaterial}
                      handleProductChange={this.handleProductChangeNewRawMaterial}
                      handleProductChangeValue={this.handleProductChangeValueNewRawMaterial}
                      handleQuantityChange={this.handleRmQuantityChange}
                      addNewRow={this.addNewRowNewRawMaterial}
                      updateData={(updatedData) => this.setState({ bomLines: updatedData })}
                      formSubmitted={formSubmitted}
                      loading={getMOByIdLoading || createMOLoading || updateMOLoading || getBOMByIdLoading}
                      isFlexible={isFlexible}
                      excludedProducts={this.getExcludedProduct()}
                      tenantDepartmentId={tenantDepartmentId}
                      multipleFG={finishedGoods?.length !== 1}
                      trackWastage={trackWastage}
                      cfRMLine={cfRMLine}
                      visibleColumns={visibleColumnsRM}
                      customLineInputChange={(value, key, tpId) => this.rmCustomLineInputChange(value, key, tpId)}
                      selectedTenant={selectedMO ? selectedMO?.tenant_id : user?.tenant_info?.tenant_id}
                      enableAdvanceSearch
                      handleMultiProductChange={this.handleMultiProductChangeNewRM}
                      finishedGoods={finishedGoods}
                      bomLinesArray={Object.values(bomLines)?.flat()}
                      bomLines={bomLines}
                      getExcludedProduct={(fgTenantProductId, parentBomId, bomId) => this.getExcludedProduct(fgTenantProductId, parentBomId, bomId)}
                    />
                  </div>
                )}

                {currentTab === '/raw-materials-summary' && (finishedGoods?.length !== 1 ? this.getMoComponents(bomLines)?.length > 0 : (finishedGoods?.length === 1 && finishedGoods?.[0]?.bom_id)) && (
                  <div className="mo-form__lines-wrapper">
                    <RawMaterial
                      handleDelete={this.handleDeleteRawMaterial}
                      handleProductChange={this.handleProductChangeRawMaterial}
                      handleProductChangeValue={this.handleProductChangeValueRawMaterial}
                      handleQuantityChange={this.handleRmQuantityChange}
                      data={this.getRawMaterialsSummary(bomLines)}
                      addNewRow={() => this.addNewRowRawMaterial()}
                      updateData={(updatedData) => this.setState({ bomLines: updatedData })}
                      formSubmitted={formSubmitted}
                      loading={getMOByIdLoading || createMOLoading || updateMOLoading || getBOMByIdLoading}
                      isFlexible={isFlexible}
                      excludedProducts={this.getExcludedProduct()}
                      tenantDepartmentId={tenantDepartmentId}
                      multipleFG={finishedGoods?.length !== 1}
                      trackWastage={trackWastage}
                      cfRMLine={cfRMLine}
                      visibleColumns={visibleColumnsRM}
                      customLineInputChange={(value, key, tpId) => this.rmCustomLineInputChange(value, key, tpId)}
                      selectedTenant={selectedMO ? selectedMO?.tenant_id : user?.tenant_info?.tenant_id}
                      enableAdvanceSearch={finishedGoods?.length === 1 && finishedGoods?.[0]?.bom_id}
                      handleMultiProductChange={this.handleMultiProductChangeRM}
                      finishedGoods={finishedGoods}
                    />
                  </div>
                )}

                {currentTab === '/by-products' && (
                  <div style={{ fontWeight: '600px', width: '70%' }}>
                    <ByProduct
                      handleDelete={this.handleDeleteByProduct}
                      handleProductChange={this.handleProductChangeByProduct}
                      handleProductChangeValue={this.handleProductChangeValueByProduct}
                      handleQuantityChange={this.handleBpQuantityChange}
                      data={bomByProducts}
                      addNewRowByProduct={({ parentData }) => this.addNewRowByProduct({ parentData })}
                      updateData={(updatedData) => {
                        this.setState({ bomByProducts: updatedData });
                      }}
                      formSubmitted={formSubmitted}
                      loading={getMOByIdLoading || createMOLoading || updateMOLoading || getBOMByIdLoading}
                      isFlexible={isFlexible}
                      excludedProducts={this.getExcludedProduct()}
                      tenantDepartmentId={tenantDepartmentId}
                      selectedTenant={selectedMO ? selectedMO?.tenant_id : user?.tenant_info?.tenant_id}
                      finishedGoods={finishedGoods}
                      enableAdvanceSearch
                      handleMultiProductChange={this.handleMultiProductChangeBP}
                      bomByProductsArray={Object.values(bomByProducts)?.flat()}
                      getExcludedProduct={(fgTenantProductId) => this.getExcludedProduct(fgTenantProductId)}
                    />
                  </div>
                )}

                {currentTab === '/additional-charges' && (
                  <div className="mo-form__lines-wrapper" style={{ width: '60%' }}>
                    <OtherCharge
                      handleDelete={this.handleDeleteOtherCharges}
                      handleChargeChange={this.handleOtherChargesChange}
                      data={finishedGoods?.length !== 1 ? this.getMoCharges(extraCharges)?.filter((item) => item?.charge_amount > 0) : this.getMoCharges(extraCharges)}
                      updateData={(updatedData) => this.setState({ extraCharges: updatedData })}
                      formSubmitted={formSubmitted}
                      loading={getMOByIdLoading || createMOLoading || updateMOLoading || getBOMByIdLoading}
                      handleProductChangeValue={this.handleProductChangeValueOtherCharge}
                      isFlexible={isFlexible}
                      multipleFG={finishedGoods?.length !== 1}
                      finishedGoods={finishedGoods}
                      extraCharges={extraCharges}
                    />
                  </div>
                )}

                {currentTab === '/job-works' && (
                  <div style={{ fontWeight: '600px', width: '100%' }}>
                    <ProductionRoute
                      handleDelete={this.handleDeleteProductionRoute}
                      data={this.getMoProductionRoutes(productionRoutes)}
                      addNewProductionRoute={() => this.addNewProductionRoute()}
                      handleRouteChange={(fgTenantProductId, key, fieldName, fieldValue) => this.handleRouteChange(fgTenantProductId, key, fieldName, fieldValue)}
                      formSubmitted={formSubmitted}
                      loading={getMOByIdLoading || createMOLoading || updateMOLoading || getBOMByIdLoading || getJobWorkByMoIdLoading}
                      isFlexible={isFlexible}
                      excludedProducts={this.getExcludedProduct()}
                      tenantDepartmentId={tenantDepartmentId}
                      multipleFG={finishedGoods?.length !== 1}
                      finishedGoods={finishedGoods}
                      rawMaterials={bomLines}
                      tenantId={selectedTenant}
                    />
                    {(finishedGoods?.length === 1 && finishedGoods?.[0]?.bom_id)
                      && (
                        <div className="new-row-button" onClick={() => (!createMOLoading && !updateMOLoading && this.addNewProductionRoute())}>
                          <span className="new-row-button__icon"><PlusCircleFilled /></span>
                          <div>New Item</div>
                        </div>
                      )}
                  </div>
                )}
              </div>

              <div className="mo-form__lines-wrapper" style={{ marginTop: '20px' }}>
                <label className="orgFormLabel">
                  Attachment(s)
                </label>
                <Upload
                  action={constants.UPLOAD_FILE}
                  listType="picture-card"
                  fileList={fileList}
                  disabled={createMOLoading || updateMOLoading}
                  multiple
                  onChange={(fileListData) => {
                    this.setState({
                      fileList: fileListData?.fileList?.map((item) => ({
                        ...item,
                        url: item?.response?.response?.location || item?.url,
                      })),
                    });
                  }}
                >
                  {fileList?.length >= 20 ? null : uploadButton}
                </Upload>
              </div>
            </div>
          )}

        <div className="mo-form__footer">
          {/* <H3Button
            text="Save as Draft"
            buttonType={defaultButtonTypes.BLUE_ROUNDED}
            customClass="mo-form__footer-draft"
            onClick={() => {
              this.setState({ actionType: 'SAVE_AS_DRAFT' });
              if (!createMOLoading) {
                this.createMO(false);
              }
            }}
            isLoading={(createMOLoading || updateMOLoading) && actionType === 'SAVE_AS_DRAFT'}
            disabled={(createMOLoading || updateMOLoading) && actionType === 'SAVE_AS_DRAFT'}
          />
          <H3Button
            text="Save and Issue"
            buttonType={defaultButtonTypes.BLUE_ROUNDED}
            customClass="mo-form__footer-submit"
            onClick={() => {
              this.setState({ actionType: 'SAVE_AND_ISSUE' });
              if (!createMOLoading) {
                this.createMO(true);
              }
            }}
            isLoading={(createMOLoading || updateMOLoading) && actionType === 'SAVE_AND_ISSUE'}
            disabled={(createMOLoading || updateMOLoading) && actionType === 'SAVE_AND_ISSUE'}
          /> */}
          {(match?.params?.moId) ? (
            <Fragment>
              <PRZConfirmationPopover
                title="Are you sure you want to update?"
                content={(
                  <Fragment>
                    <PRZText text="Reason" required />
                    <PRZInput
                      placeholder="Enter update reason"
                      value={updateDocumentReason}
                      onChange={(e) => this.setState({ updateDocumentReason: e.target.value })}
                    />
                  </Fragment>
                )}
                onConfirm={() => {
                  this.setState({ actionType: 'SAVE_AS_DRAFT' });
                  if (!createMOLoading) {
                    this.createMO(false);
                  }
                }}
                confirmButtonText="Confirm"
                cancelButtonText="Back"
                confirmDisabled={!updateDocumentReason}
              >
                <PRZButton
                  type="default"
                  wrapperStyle={{ marginRight: '10px' }}
                  buttonStyle={{
                    width: '130px',
                    height: '40px',
                    border: '1px solid #2d7df7',
                    color: '#2d7df7',
                  }}
                  isLoading={(createMOLoading || updateMOLoading) && actionType === 'SAVE_AS_DRAFT'}
                  disabled={(createMOLoading || updateMOLoading) && actionType === 'SAVE_AS_DRAFT'}
                >
                  Save as Draft
                </PRZButton>
              </PRZConfirmationPopover>
              <PRZConfirmationPopover
                title="Are you sure you want to update?"
                content={(
                  <Fragment>
                    <PRZText text="Reason" required />
                    <PRZInput
                      placeholder="Enter update reason"
                      value={updateDocumentReason}
                      onChange={(e) => this.setState({ updateDocumentReason: e.target.value })}
                    />
                  </Fragment>
                )}
                onConfirm={() => {
                  this.setState({ actionType: 'SAVE_AND_ISSUE' });
                  if (!createMOLoading) {
                    this.createMO(true);
                  }
                }}
                confirmButtonText="Confirm"
                cancelButtonText="Back"
                confirmDisabled={!updateDocumentReason}
              >
                <PRZButton
                  isLoading={(createMOLoading || updateMOLoading) && actionType === 'SAVE_AND_ISSUE'}
                  disabled={(createMOLoading || updateMOLoading) && actionType === 'SAVE_AND_ISSUE'}
                  buttonStyle={{ width: '130px', height: '40px' }}
                >
                  Save and Issue
                </PRZButton>
              </PRZConfirmationPopover>
            </Fragment>
          ) : (
            <Fragment>
              <PRZButton
                type="default"
                onClick={() => {
                  this.setState({ actionType: 'SAVE_AS_DRAFT' });
                  if (!createMOLoading) {
                    this.createMO(false);
                  }
                }}
                isLoading={(createMOLoading || updateMOLoading) && actionType === 'SAVE_AS_DRAFT'}
                disabled={(createMOLoading || updateMOLoading) && actionType === 'SAVE_AS_DRAFT'}
                wrapperStyle={{ marginRight: '10px' }}
                buttonStyle={{
                  width: '130px',
                  height: '40px',
                  border: '1px solid #2d7df7',
                  color: '#2d7df7',
                }}
              >
                Save as Draft
              </PRZButton>
              <PRZButton
                onClick={() => {
                  this.setState({ actionType: 'SAVE_AND_ISSUE' });
                  if (!createMOLoading) {
                    this.createMO(true);
                  }
                }}
                isLoading={(createMOLoading || updateMOLoading) && actionType === 'SAVE_AND_ISSUE'}
                disabled={(createMOLoading || updateMOLoading) && actionType === 'SAVE_AND_ISSUE'}
                buttonStyle={{ width: '130px', height: '40px' }}
              >
                Save and Issue
              </PRZButton>
            </Fragment>
          )}
          {isDataMaskingPolicyEnable && (isHideCostPrice || isHideSellingPrice) && <RestrictedAccessMessage message={`You don't have access to view or edit${isHideCostPrice ? ' unit price/cost price' : ''}${isHideCostPrice && isHideSellingPrice ? ' and' : ''} ${isHideSellingPrice ? 'selling price' : ''}`} />}
        </div>
        <Drawer
          open={showAddressDrawer}
          onClose={() => this.setState({ showAddressDrawer: false })}
          width="360"
          destroyOnClose
        >
          <AddressSelector
            title="Vendor Address"
            addressType="SELLER"
            selectedAddressId={vendorAddress?.address_id}
            onAddressChange={(address) => {
              this.setState({ vendorAddress: address, showAddressDrawer: false });
            }}
            entityId={sellerId}
            entityType="SELLER"
            seller={sellerId}
            tenantId={selectedTenant || selectedMO?.tenant_id}
          />
        </Drawer>
      </Fragment>

    );
  }
}

const mapStateToProps = ({
  UserReducers, MOReducers, CFV2Reducers, BOMReducers, JobWorkReducers, ProductReducers, CMReducers,
}) => ({
  user: UserReducers.user,
  createMOLoading: MOReducers.createMOLoading,
  updateMOLoading: MOReducers.updateMOLoading,
  getMOByIdLoading: MOReducers.getMOByIdLoading,
  selectedMO: MOReducers.selectedMO,
  getDocCFV2Loading: CFV2Reducers.getDocCFV2Loading,
  cfV2DocManufacturingOrder: CFV2Reducers.cfV2DocManufacturingOrder,
  getBOMByIdLoading: BOMReducers.getBOMByIdLoading,
  moJobWorks: MOReducers.moJobWorks,
  priceMasking: UserReducers.priceMasking,
  getProductByIdLoading: ProductReducers.getProductByIdLoading,
  org: UserReducers.org,
  customMessageBillOfMaterial: CMReducers.customMessageBillOfMaterial,
  getcustomMessageBillOfMaterialLoading: CMReducers.getcustomMessageBillOfMaterialLoading,
});

const mapDispatchToProps = (dispatch) => ({
  createMO: (payload, callback) => dispatch(MOActions.createMO(payload, callback)),
  updateMO: (payload, callback) => dispatch(MOActions.updateMO(payload, callback)),
  getMOByIdSuccess: (selectedMO) => dispatch(MOActions.getMOByIdSuccess(selectedMO)),
  getTenantSkuOffer: (tenantProductId, tenantSellerId, callback) => dispatch(OfferActions.getOfferByTenantSku(tenantProductId, tenantSellerId, callback)),
  getBOM: (orgId, tenantId, bomId, productSkuId, status, page, limit, searchKeyword, tenantDepartmentId, responseType) => dispatch(BOMActions.getBOM(orgId, tenantId, bomId, productSkuId, status, page, limit, searchKeyword, tenantDepartmentId, responseType)),
  getBOMById: (orgId, tenantId, bomId, tenantDepartmentId, bomViewType, callback) => dispatch(BOMActions.getBOMById(orgId, tenantId, bomId, tenantDepartmentId, bomViewType, callback)),
  getMOById: (tenantId, soId) => dispatch(MOActions.getMOById(tenantId, soId)),
  getJobWorkByMoId: (orgId, tenantId, moId) => dispatch(JobWorkActions.getJobWorkByMoId(orgId, tenantId, moId)),
  getJobWorkByMoIdSuccess: (jobWorks) => dispatch(JobWorkActions.getJobWorkByMoIdSuccess(jobWorks)),
  getDocCFV2Success: (customFields) => dispatch(CFV2Actions.getDocCFV2Success(customFields)),
  getDocCFV2: (payload, callback) => dispatch(CFV2Actions.getDocCFV2(payload, callback)),
  getMoJobWorks: (payload, callback) => dispatch(MOActions.getMoJobWorks(payload, callback)),
  getBOMByIdSuccess: (selectedBOM) => dispatch(BOMActions.getBOMByIdSuccess(selectedBOM)),
  getProductById: (
    tenantId,
    productSkuId,
    tenantDepartmentId,
    callback,
    filterReservedBatches,
    productSkuIds,
  ) => dispatch(
    ProductActions.getProductById(
      tenantId,
      productSkuId,
      tenantDepartmentId,
      callback,
      filterReservedBatches,
      productSkuIds,
    ),
  ),
  getCM: (orgId, entityId, entityType, page, limit, tenantId, actionType) => dispatch(CMActions.getCM(orgId, entityId, entityType, page, limit, tenantId, actionType)),
});

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(ManufacturingOrderForm));
